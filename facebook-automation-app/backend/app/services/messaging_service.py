"""
Bulk messaging service with multi-threading, rate limiting, and delivery tracking.
"""
import asyncio
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from concurrent.futures import ThreadPoolExecutor
import threading
from queue import Queue, Empty

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from loguru import logger

from ..models.messaging import (
    MessageCampaign, Message, MessageQueue, MessageTemplate,
    MessageStatus, CampaignStatus, MessageType
)
from ..models.scraped_data import ScrapedUser
from ..models.profile import Profile
from ..core.config import settings
from ..utils.session_manager import browser_session_manager
from .template_service import template_service


class RateLimiter:
    """Rate limiter for message sending."""
    
    def __init__(self, messages_per_minute: int = 10):
        self.messages_per_minute = messages_per_minute
        self.min_delay = 60.0 / messages_per_minute  # Minimum delay between messages
        self.last_send_time = 0.0
        self.lock = threading.Lock()
    
    async def wait_if_needed(self):
        """Wait if rate limit requires it."""
        with self.lock:
            current_time = asyncio.get_event_loop().time()
            time_since_last = current_time - self.last_send_time
            
            if time_since_last < self.min_delay:
                wait_time = self.min_delay - time_since_last
                logger.debug(f"Rate limiting: waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
            
            self.last_send_time = asyncio.get_event_loop().time()
    
    def update_rate(self, messages_per_minute: int):
        """Update rate limit."""
        with self.lock:
            self.messages_per_minute = messages_per_minute
            self.min_delay = 60.0 / messages_per_minute


class MessageWorker:
    """Worker thread for sending messages."""
    
    def __init__(self, worker_id: str, rate_limiter: RateLimiter):
        self.worker_id = worker_id
        self.rate_limiter = rate_limiter
        self.is_running = False
        self.messages_sent = 0
        self.messages_failed = 0
    
    async def send_message(
        self,
        profile: Profile,
        message: Message,
        db: AsyncSession
    ) -> bool:
        """Send individual message."""
        try:
            # Wait for rate limiting
            await self.rate_limiter.wait_if_needed()
            
            # Update message status
            message.status = MessageStatus.SENDING.value
            await db.commit()
            
            # Simulate message sending with browser
            success = await self._send_facebook_message(profile, message)
            
            if success:
                message.mark_as_sent(f"fb_msg_{random.randint(100000, 999999)}")
                self.messages_sent += 1
                logger.info("Worker {}: Message sent to {}", self.worker_id, message.target_uid)
            else:
                message.mark_as_failed("Failed to send message")
                self.messages_failed += 1
                logger.warning("Worker {}: Failed to send message to {}", self.worker_id, message.target_uid)
            
            await db.commit()
            return success
            
        except Exception as e:
            message.mark_as_failed(str(e))
            self.messages_failed += 1
            await db.commit()
            logger.error(f"Worker {self.worker_id}: Error sending message: {e}")
            return False
    
    async def _send_facebook_message(self, profile: Profile, message: Message) -> bool:
        """Send message through Facebook (mock implementation)."""
        try:
            # In real implementation, use browser session to send message
            async with browser_session_manager.get_browser_session(profile) as browser:
                # Navigate to Facebook messages
                page = await browser.get("https://facebook.com/messages")
                
                # Simulate message sending process
                await asyncio.sleep(random.uniform(2, 5))  # Simulate typing and sending
                
                # Mock success rate (90% success in testing)
                return random.random() > 0.1
                
        except Exception as e:
            logger.error("Facebook message sending error: {}", str(e))
            return False


class BulkMessagingEngine:
    """
    Advanced bulk messaging engine with multi-threading and queue management.
    """
    
    def __init__(self):
        self.max_workers = settings.max_concurrent_senders
        self.workers: Dict[str, MessageWorker] = {}
        self.active_campaigns: Set[int] = set()
        self.campaign_locks: Dict[int, asyncio.Lock] = {}
    
    async def create_campaign(
        self,
        db: AsyncSession,
        name: str,
        template_id: int,
        profile_id: int,
        scraping_session_id: int,
        target_filters: Optional[Dict[str, Any]] = None,
        messages_per_minute: int = 10,
        scheduled_at: Optional[datetime] = None
    ) -> MessageCampaign:
        """Create new messaging campaign."""
        
        # Validate template exists
        template = await template_service.get_template(db, template_id)
        if not template:
            raise ValueError(f"Template {template_id} not found")
        
        # Validate profile exists
        profile = await db.get(Profile, profile_id)
        if not profile:
            raise ValueError(f"Profile {profile_id} not found")
        
        # Get target users from scraping session
        target_users = await self._get_target_users(db, scraping_session_id, target_filters or {})
        
        if not target_users:
            raise ValueError("No target users found for campaign")
        
        # Create campaign
        campaign = MessageCampaign(
            name=name,
            template_id=template_id,
            profile_id=profile_id,
            scraping_session_id=scraping_session_id,
            target_user_count=len(target_users),
            target_filters=target_filters or {},
            messages_per_minute=messages_per_minute,
            delay_between_messages=60.0 / messages_per_minute,
            scheduled_at=scheduled_at,
            status=CampaignStatus.DRAFT.value
        )
        
        db.add(campaign)
        await db.commit()
        await db.refresh(campaign)
        
        # Create messages for target users
        await self._create_campaign_messages(db, campaign, template, target_users)
        
        logger.info(f"Created campaign '{name}' with {len(target_users)} target users")
        return campaign
    
    async def _get_target_users(
        self,
        db: AsyncSession,
        scraping_session_id: int,
        filters: Dict[str, Any]
    ) -> List[ScrapedUser]:
        """Get target users based on filters."""
        
        query = select(ScrapedUser).where(
            ScrapedUser.session_id == scraping_session_id,
            ScrapedUser.message_sent == False  # Only users who haven't been messaged
        )
        
        # Apply filters
        if "gender" in filters:
            query = query.where(ScrapedUser.gender == filters["gender"])
        
        if "interaction_types" in filters:
            query = query.where(ScrapedUser.interaction_type.in_(filters["interaction_types"]))
        
        if "exclude_uids" in filters:
            query = query.where(~ScrapedUser.uid.in_(filters["exclude_uids"]))
        
        # Limit number of users if specified
        if "max_users" in filters:
            query = query.limit(filters["max_users"])
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def _create_campaign_messages(
        self,
        db: AsyncSession,
        campaign: MessageCampaign,
        template: MessageTemplate,
        target_users: List[ScrapedUser]
    ):
        """Create individual messages for campaign."""
        
        messages_created = 0
        
        for user in target_users:
            # Prepare user data for template rendering
            user_data = {
                "name": user.name or "Bạn",
                "uid": user.uid,
                "profile_url": user.profile_url or ""
            }
            
            # Render message content
            try:
                content = await template_service.render_template(
                    db, template.id, user_data
                )
            except Exception as e:
                logger.warning(f"Failed to render template for user {user.uid}: {e}")
                content = template.content  # Use original content as fallback
            
            # Create message
            message = Message(
                campaign_id=campaign.id,
                template_id=template.id,
                target_uid=user.uid,
                target_name=user.name,
                target_profile_url=user.profile_url,
                content=content,
                message_type=template.message_type,
                variables_used=user_data,
                status=MessageStatus.PENDING.value
            )
            
            db.add(message)
            messages_created += 1
            
            # Create queue entry
            queue_entry = MessageQueue(
                campaign_id=campaign.id,
                message_id=message.id,  # Will be set after flush
                status="queued",
                priority=0
            )
            
            # Flush to get message ID
            await db.flush()
            queue_entry.message_id = message.id
            db.add(queue_entry)
        
        # Update campaign statistics
        campaign.total_messages = messages_created
        
        await db.commit()
        logger.info(f"Created {messages_created} messages for campaign {campaign.id}")
    
    async def start_campaign(self, db: AsyncSession, campaign_id: int) -> bool:
        """Start messaging campaign."""
        
        campaign = await db.get(MessageCampaign, campaign_id)
        if not campaign:
            raise ValueError(f"Campaign {campaign_id} not found")
        
        if campaign.status not in [CampaignStatus.DRAFT.value, CampaignStatus.PAUSED.value]:
            raise ValueError(f"Campaign {campaign_id} cannot be started (status: {campaign.status})")
        
        # Check if scheduled
        if campaign.scheduled_at and campaign.scheduled_at > datetime.utcnow():
            raise ValueError(f"Campaign {campaign_id} is scheduled for {campaign.scheduled_at}")
        
        # Update campaign status
        campaign.status = CampaignStatus.RUNNING.value
        campaign.started_at = datetime.utcnow()
        campaign.current_step = "Starting campaign..."
        await db.commit()
        
        # Add to active campaigns
        self.active_campaigns.add(campaign_id)
        self.campaign_locks[campaign_id] = asyncio.Lock()
        
        # Start processing in background
        asyncio.create_task(self._process_campaign(db, campaign))
        
        logger.info(f"Started campaign {campaign_id}")
        return True
    
    async def _process_campaign(self, db: AsyncSession, campaign: MessageCampaign):
        """Process campaign messages with workers."""
        
        try:
            # Get profile for sending
            profile = await db.get(Profile, campaign.profile_id)
            if not profile:
                raise ValueError(f"Profile {campaign.profile_id} not found")
            
            # Create rate limiter
            rate_limiter = RateLimiter(campaign.messages_per_minute)
            
            # Create workers
            workers = []
            for i in range(min(self.max_workers, campaign.total_messages)):
                worker = MessageWorker(f"worker_{campaign.id}_{i}", rate_limiter)
                workers.append(worker)
                self.workers[worker.worker_id] = worker
            
            # Process messages
            await self._process_campaign_messages(db, campaign, profile, workers)
            
            # Update campaign completion
            campaign.status = CampaignStatus.COMPLETED.value
            campaign.completed_at = datetime.utcnow()
            campaign.current_step = "Completed"
            campaign.progress_percentage = 100.0
            
            # Calculate final statistics
            await self._update_campaign_statistics(db, campaign)
            
            await db.commit()
            
            logger.info(f"Campaign {campaign.id} completed successfully")
            
        except Exception as e:
            # Mark campaign as failed
            campaign.status = CampaignStatus.CANCELLED.value
            campaign.completed_at = datetime.utcnow()
            campaign.current_step = f"Failed: {str(e)}"
            await db.commit()
            
            logger.error(f"Campaign {campaign.id} failed: {e}")
        
        finally:
            # Clean up
            self.active_campaigns.discard(campaign.id)
            self.campaign_locks.pop(campaign.id, None)
            
            # Remove workers
            for worker in workers:
                self.workers.pop(worker.worker_id, None)
    
    async def _process_campaign_messages(
        self,
        db: AsyncSession,
        campaign: MessageCampaign,
        profile: Profile,
        workers: List[MessageWorker]
    ):
        """Process all messages in campaign using workers."""
        
        # Get pending messages
        pending_messages = await db.execute(
            select(Message).where(
                Message.campaign_id == campaign.id,
                Message.status == MessageStatus.PENDING.value
            ).order_by(Message.id)
        )
        messages = pending_messages.scalars().all()
        
        if not messages:
            logger.warning(f"No pending messages found for campaign {campaign.id}")
            return
        
        # Process messages with workers
        total_messages = len(messages)
        processed_count = 0
        
        # Create semaphore to limit concurrent workers
        semaphore = asyncio.Semaphore(len(workers))
        
        async def process_message(message: Message, worker: MessageWorker):
            nonlocal processed_count
            
            async with semaphore:
                success = await worker.send_message(profile, message, db)
                
                processed_count += 1
                progress = (processed_count / total_messages) * 100
                
                # Update campaign progress
                campaign.progress_percentage = progress
                campaign.current_step = f"Sent {processed_count}/{total_messages} messages"
                
                if processed_count % 10 == 0:  # Update every 10 messages
                    await db.commit()
                
                return success
        
        # Create tasks for all messages
        tasks = []
        for i, message in enumerate(messages):
            worker = workers[i % len(workers)]  # Round-robin worker assignment
            task = asyncio.create_task(process_message(message, worker))
            tasks.append(task)
        
        # Wait for all messages to be processed
        await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"Processed {processed_count} messages for campaign {campaign.id}")
    
    async def _update_campaign_statistics(self, db: AsyncSession, campaign: MessageCampaign):
        """Update campaign statistics."""
        
        # Count messages by status
        stats = await db.execute(
            select(
                Message.status,
                func.count(Message.id)
            ).where(
                Message.campaign_id == campaign.id
            ).group_by(Message.status)
        )
        
        status_counts = dict(stats.fetchall())
        
        campaign.sent_messages = status_counts.get(MessageStatus.SENT.value, 0)
        campaign.delivered_messages = status_counts.get(MessageStatus.DELIVERED.value, 0)
        campaign.failed_messages = status_counts.get(MessageStatus.FAILED.value, 0)
        
        logger.info(f"Campaign {campaign.id} statistics updated: "
                   f"{campaign.sent_messages} sent, {campaign.failed_messages} failed")
    
    async def pause_campaign(self, db: AsyncSession, campaign_id: int) -> bool:
        """Pause running campaign."""
        
        campaign = await db.get(MessageCampaign, campaign_id)
        if not campaign or campaign.status != CampaignStatus.RUNNING.value:
            return False
        
        campaign.status = CampaignStatus.PAUSED.value
        campaign.current_step = "Paused by user"
        await db.commit()
        
        # Remove from active campaigns
        self.active_campaigns.discard(campaign_id)
        
        logger.info(f"Paused campaign {campaign_id}")
        return True
    
    async def cancel_campaign(self, db: AsyncSession, campaign_id: int) -> bool:
        """Cancel campaign."""
        
        campaign = await db.get(MessageCampaign, campaign_id)
        if not campaign:
            return False
        
        campaign.status = CampaignStatus.CANCELLED.value
        campaign.completed_at = datetime.utcnow()
        campaign.current_step = "Cancelled by user"
        await db.commit()
        
        # Remove from active campaigns
        self.active_campaigns.discard(campaign_id)
        
        logger.info(f"Cancelled campaign {campaign_id}")
        return True
    
    def get_campaign_status(self, campaign_id: int) -> Dict[str, Any]:
        """Get real-time campaign status."""
        
        is_active = campaign_id in self.active_campaigns
        worker_count = len([w for w in self.workers.values() if str(campaign_id) in w.worker_id])
        
        return {
            "campaign_id": campaign_id,
            "is_active": is_active,
            "worker_count": worker_count,
            "total_workers": len(self.workers)
        }


# Global messaging engine instance
messaging_engine = BulkMessagingEngine()
