"""
Security utilities for the application.
"""
import secrets
from typing import Optional
from cryptography.fernet import <PERSON><PERSON><PERSON>
from loguru import logger

from .config import settings


class SecurityManager:
    """
    Security manager for encryption and data protection.
    """
    
    def __init__(self):
        self._fernet = None
        self._init_encryption()
    
    def _init_encryption(self):
        """Initialize encryption with key from settings or generate new one."""
        if settings.encryption_key:
            try:
                self._fernet = Fernet(settings.encryption_key.encode())
            except Exception as e:
                logger.warning(f"Invalid encryption key in settings: {e}")
                self._fernet = None
        
        if not self._fernet:
            # Generate new key for this session
            key = Fernet.generate_key()
            self._fernet = Fernet(key)
            logger.info("Generated new encryption key for session")
    
    def encrypt_string(self, plaintext: str) -> str:
        """Encrypt a string and return base64 encoded result."""
        if not plaintext:
            return ""
        
        try:
            encrypted = self._fernet.encrypt(plaintext.encode())
            return encrypted.decode()
        except Exception as e:
            logger.error("Encryption failed: {}", str(e))
            return plaintext  # Return original if encryption fails
    
    def decrypt_string(self, encrypted_text: str) -> str:
        """Decrypt a base64 encoded encrypted string."""
        if not encrypted_text:
            return ""
        
        try:
            decrypted = self._fernet.decrypt(encrypted_text.encode())
            return decrypted.decode()
        except Exception as e:
            logger.error("Decryption failed: {}", str(e))
            return encrypted_text  # Return original if decryption fails
    
    def generate_session_token(self) -> str:
        """Generate a secure session token."""
        return secrets.token_urlsafe(32)
    
    def generate_api_key(self) -> str:
        """Generate a secure API key."""
        return secrets.token_urlsafe(48)


# Global security manager instance
security_manager = SecurityManager()
