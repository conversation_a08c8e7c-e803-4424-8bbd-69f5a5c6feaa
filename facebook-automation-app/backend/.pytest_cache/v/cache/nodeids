["tests/test_facebook_scraper_service.py::TestErrorHandler::test_error_classification", "tests/test_facebook_scraper_service.py::TestErrorHandler::test_retry_logic", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_auto_optimization", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_cleanup", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_deduplication", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_error_handling", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_memory_optimization", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_multiple_posts_scraping", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_parallel_processing", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_performance_monitoring", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_service_initialization", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_single_post_scraping", "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_statistics_collection", "tests/test_facebook_scraper_service.py::TestMemoryOptimizer::test_memory_monitoring", "tests/test_facebook_scraper_service.py::TestMemoryOptimizer::test_streaming_processing", "tests/test_facebook_scraper_service.py::TestParallelProcessor::test_resource_monitoring", "tests/test_facebook_scraper_service.py::TestParallelProcessor::test_task_processing", "tests/test_facebook_scraper_service.py::TestPerformanceMonitor::test_anomaly_detection", "tests/test_facebook_scraper_service.py::TestPerformanceMonitor::test_metrics_collection", "tests/test_facebook_scraper_service.py::TestPerformanceMonitor::test_operation_profiling"]