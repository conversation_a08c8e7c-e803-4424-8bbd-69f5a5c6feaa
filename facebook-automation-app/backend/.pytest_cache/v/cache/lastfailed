{"tests/test_integration.py": true, "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_multiple_posts_scraping": true, "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_parallel_processing": true, "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_memory_optimization": true, "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_auto_optimization": true, "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_error_handling": true, "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_deduplication": true, "tests/test_facebook_scraper_service.py::TestFacebookScraperService::test_statistics_collection": true, "tests/test_facebook_scraper_service.py::TestParallelProcessor::test_resource_monitoring": true, "tests/test_facebook_scraper_service.py::TestParallelProcessor::test_task_processing": true, "tests/test_facebook_scraper_service.py::TestMemoryOptimizer::test_memory_monitoring": true, "tests/test_facebook_scraper_service.py::TestMemoryOptimizer::test_streaming_processing": true, "tests/test_facebook_scraper_service.py::TestPerformanceMonitor::test_operation_profiling": true, "tests/test_facebook_scraper_service.py::TestErrorHandler::test_retry_logic": true}