# Facebook Automation App - Backend

Advanced Facebook scraping system with antidetect browser profiles, parallel processing, and comprehensive optimization features.

## 🚀 Features

### Core Functionality
- **Facebook UID Extraction**: Extract user IDs from Facebook posts and comments
- **Antidetect Browser Profiles**: Stealth browsing with custom fingerprints
- **Parallel Processing**: High-performance concurrent scraping
- **Memory Optimization**: Efficient processing of large datasets
- **Performance Monitoring**: Real-time system metrics and anomaly detection
- **Error Handling**: Intelligent retry strategies and recovery mechanisms

### Advanced Optimization
- **Auto Optimization**: Automatic selection of best processing strategy
- **Streaming Processing**: Memory-efficient handling of large datasets
- **Resource Management**: Dynamic scaling based on system resources
- **Deduplication**: Intelligent UID deduplication across sessions
- **Progress Tracking**: Real-time progress reporting with callbacks

## 📋 Requirements

- Python 3.10+
- zendriver (antidetect browser automation)
- FastAPI (REST API)
- SQLAlchemy (database ORM)
- psutil (system monitoring)

## 🛠️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd facebook-automation-app/backend
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Setup database**
```bash
python -c "from database.models import create_tables; create_tables()"
```

## 🚀 Quick Start

### 1. Start the API Server
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Basic Usage Example
```python
from automation.facebook_scraper_service import FacebookScraperService

# Initialize service
scraper = FacebookScraperService()

# Scrape a single post
result = await scraper.scrape_facebook_post_uids(
    profile_id="your_profile_id",
    post_url="https://www.facebook.com/groups/123456789/posts/987654321/"
)

print(f"Found {result['results']['total_uids_found']} UIDs")
```

### 3. Advanced Parallel Scraping
```python
# Scrape multiple posts in parallel
result = await scraper.scrape_multiple_posts_parallel(
    profile_id="your_profile_id",
    post_urls=[
        "https://www.facebook.com/groups/123456789/posts/1/",
        "https://www.facebook.com/groups/123456789/posts/2/",
        "https://www.facebook.com/groups/123456789/posts/3/"
    ],
    max_concurrent=3
)
```

### 4. Memory-Optimized Processing
```python
# For large datasets
result = await scraper.scrape_with_memory_optimization(
    profile_id="your_profile_id",
    post_urls=large_post_list
)
```

## 🔧 API Endpoints

### Advanced Scraping
```http
POST /api/scraping/advanced-scraping
Content-Type: application/json

{
    "profile_id": "your_profile",
    "post_urls": ["https://facebook.com/post/1"],
    "optimization_mode": "auto",
    "enable_performance_monitoring": true,
    "session_name": "My Scraping Session"
}
```

### Parallel Processing
```http
POST /api/scraping/parallel-scraping
Content-Type: application/json

{
    "profile_id": "your_profile",
    "post_urls": ["url1", "url2", "url3"],
    "max_concurrent": 3,
    "session_name": "Parallel Session"
}
```

### Memory Optimization
```http
POST /api/scraping/memory-optimized-scraping
Content-Type: application/json

{
    "profile_id": "your_profile",
    "post_urls": ["url1", "url2"],
    "session_name": "Memory Optimized Session"
}
```

### Performance Monitoring
```http
GET /api/scraping/performance-report
```

### Statistics
```http
GET /api/scraping/statistics
```

## 🧪 Testing

### Run All Tests
```bash
python run_tests.py --type all --verbose
```

### Run Specific Test Types
```bash
# Unit tests only
python run_tests.py --type unit

# Integration tests only
python run_tests.py --type integration

# Performance tests only
python run_tests.py --type performance
```

### Performance Testing
```bash
# Mock performance test
python performance_test.py --mock

# Real performance test
python performance_test.py \
    --profile-id "your_profile" \
    --post-urls "https://facebook.com/post/1" "https://facebook.com/post/2" \
    --single-iterations 5 \
    --parallel-concurrency 1 2 4 \
    --stress-minutes 5
```

## 📊 Performance Optimization

### Optimization Modes

1. **Auto Mode**: Automatically selects best strategy based on workload
2. **Parallel Mode**: Uses concurrent processing for multiple posts
3. **Memory Optimized**: Streams processing for large datasets
4. **Streaming Mode**: Continuous processing with minimal memory usage

### Performance Monitoring

The system includes comprehensive performance monitoring:

- **System Metrics**: CPU, memory, disk I/O, network usage
- **Operation Metrics**: Response times, throughput, error rates
- **Anomaly Detection**: Automatic detection of performance issues
- **Trend Analysis**: Performance trends over time
- **Resource Management**: Dynamic scaling based on system load

### Memory Management

- **Streaming Processing**: Process large datasets without loading everything into memory
- **Garbage Collection**: Automatic cleanup of unused objects
- **Memory Monitoring**: Real-time memory usage tracking
- **Memory Limits**: Configurable memory usage limits

## 🔧 Configuration

### Browser Profiles
```python
profile_config = {
    "profile_id": "unique_profile_id",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "screen_resolution": "1920x1080",
    "timezone": "America/New_York",
    "proxy": {
        "type": "http",  # http, https, socks5, ssh
        "host": "proxy.example.com",
        "port": 8080,
        "username": "user",
        "password": "pass"
    }
}
```

### Performance Settings
```python
# Parallel processing
max_concurrent_browsers = 5
max_concurrent_posts = 10

# Memory optimization
max_memory_mb = 2048
streaming_chunk_size = 100

# Error handling
max_retries = 3
retry_delay = 5.0
```

## 📁 Project Structure

```
backend/
├── automation/           # Core automation modules
│   ├── facebook_scraper_service.py    # Main service
│   ├── parallel_processor.py          # Parallel processing
│   ├── memory_optimizer.py            # Memory optimization
│   ├── performance_monitor.py         # Performance monitoring
│   ├── error_handler.py               # Error handling
│   └── deduplication_system.py        # UID deduplication
├── api/                  # REST API endpoints
│   └── scraping.py       # Scraping endpoints
├── database/             # Database models and operations
│   └── models.py         # SQLAlchemy models
├── tests/                # Test suites
│   ├── test_facebook_scraper_service.py
│   ├── test_integration.py
│   └── conftest.py       # Test configuration
├── run_tests.py          # Test runner
├── performance_test.py   # Performance testing
└── main.py              # FastAPI application
```

## 🐛 Troubleshooting

### Common Issues

1. **zendriver Installation Issues**
   ```bash
   pip install --upgrade zendriver
   ```

2. **Memory Issues with Large Datasets**
   - Use memory optimization mode
   - Reduce concurrent processing
   - Enable streaming processing

3. **Facebook Rate Limiting**
   - Reduce scraping speed
   - Use different browser profiles
   - Implement delays between requests

4. **Browser Profile Issues**
   - Verify profile configuration
   - Check proxy settings
   - Ensure profile directory exists

### Debug Mode
```bash
export LOG_LEVEL=DEBUG
python main.py
```

## 📈 Performance Benchmarks

Typical performance on modern hardware:

- **Single Post**: 15-45 seconds (depending on comments)
- **Parallel Processing**: 2-5x speedup with 3-5 concurrent workers
- **Memory Usage**: 50-200MB per browser instance
- **Throughput**: 50-200 posts per hour (depending on complexity)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Related Projects

- [zendriver](https://github.com/ultrafunkamsterdam/zendriver) - Antidetect browser automation
- [FastAPI](https://fastapi.tiangolo.com/) - Modern web framework
- [SQLAlchemy](https://www.sqlalchemy.org/) - Database toolkit

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the test examples for usage patterns
