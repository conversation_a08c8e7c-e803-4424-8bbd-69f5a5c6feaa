"""
Comprehensive Error Handling and Retry Logic for Facebook Scraping
Handles various error scenarios with intelligent retry strategies and fallback mechanisms
"""

import asyncio
import time
import random
from typing import Dict, Any, List, Optional, Callable, Union, Type
from enum import Enum
from dataclasses import dataclass, field
from loguru import logger
import traceback
from functools import wraps
import inspect


class ErrorType(Enum):
    """Classification of error types for appropriate handling"""
    NETWORK_ERROR = "network_error"
    BROWSER_ERROR = "browser_error"
    FACEBOOK_ERROR = "facebook_error"
    AUTHENTICATION_ERROR = "authentication_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    PARSING_ERROR = "parsing_error"
    RESOURCE_ERROR = "resource_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"


class RetryStrategy(Enum):
    """Different retry strategies"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIXED_DELAY = "fixed_delay"
    FIBONACCI_BACKOFF = "fibonacci_backoff"
    NO_RETRY = "no_retry"


@dataclass
class ErrorContext:
    """Context information for error handling"""
    error_type: ErrorType
    original_exception: Exception
    function_name: str
    attempt_number: int
    total_attempts: int
    elapsed_time: float
    additional_info: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_multiplier: float = 2.0
    jitter: bool = True
    retry_on_errors: List[ErrorType] = field(default_factory=lambda: [
        ErrorType.NETWORK_ERROR,
        ErrorType.BROWSER_ERROR,
        ErrorType.TIMEOUT_ERROR,
        ErrorType.RATE_LIMIT_ERROR
    ])
    no_retry_on_errors: List[ErrorType] = field(default_factory=lambda: [
        ErrorType.AUTHENTICATION_ERROR,
        ErrorType.PARSING_ERROR
    ])


class FacebookErrorClassifier:
    """Classify errors into appropriate types for handling"""
    
    ERROR_PATTERNS = {
        ErrorType.NETWORK_ERROR: [
            "connection", "network", "timeout", "dns", "socket",
            "connection refused", "connection reset", "unreachable"
        ],
        ErrorType.BROWSER_ERROR: [
            "browser", "chrome", "selenium", "webdriver", "zendriver",
            "page crash", "browser crash", "navigation"
        ],
        ErrorType.FACEBOOK_ERROR: [
            "facebook", "fb", "login required", "session expired",
            "blocked", "restricted", "unavailable"
        ],
        ErrorType.AUTHENTICATION_ERROR: [
            "authentication", "login", "password", "2fa", "security",
            "unauthorized", "forbidden", "access denied"
        ],
        ErrorType.RATE_LIMIT_ERROR: [
            "rate limit", "too many requests", "throttled", "quota",
            "limit exceeded", "temporarily blocked"
        ],
        ErrorType.PARSING_ERROR: [
            "parsing", "parse", "json", "html", "xpath", "selector",
            "element not found", "invalid format"
        ],
        ErrorType.RESOURCE_ERROR: [
            "memory", "cpu", "disk", "resource", "out of memory",
            "system overload", "insufficient resources"
        ],
        ErrorType.TIMEOUT_ERROR: [
            "timeout", "timed out", "deadline", "expired"
        ]
    }
    
    @classmethod
    def classify_error(cls, exception: Exception) -> ErrorType:
        """Classify an exception into an error type"""
        try:
            error_message = str(exception).lower()
            exception_type = type(exception).__name__.lower()
            
            # Check exception type first
            if "timeout" in exception_type:
                return ErrorType.TIMEOUT_ERROR
            elif "network" in exception_type or "connection" in exception_type:
                return ErrorType.NETWORK_ERROR
            elif "authentication" in exception_type or "unauthorized" in exception_type:
                return ErrorType.AUTHENTICATION_ERROR
            
            # Check error message patterns
            for error_type, patterns in cls.ERROR_PATTERNS.items():
                for pattern in patterns:
                    if pattern in error_message:
                        return error_type
            
            return ErrorType.UNKNOWN_ERROR
            
        except Exception:
            return ErrorType.UNKNOWN_ERROR


class RetryHandler:
    """Handle retry logic with different strategies"""
    
    def __init__(self, config: RetryConfig):
        self.config = config
        self.fibonacci_cache = [1, 1]
    
    def should_retry(self, error_context: ErrorContext) -> bool:
        """Determine if an error should be retried"""
        # Check if we've exceeded max attempts
        if error_context.attempt_number >= self.config.max_attempts:
            return False
        
        # Check if error type is in no-retry list
        if error_context.error_type in self.config.no_retry_on_errors:
            return False
        
        # Check if error type is in retry list
        if error_context.error_type in self.config.retry_on_errors:
            return True
        
        # Default: don't retry unknown errors after first attempt
        return error_context.attempt_number == 1
    
    def calculate_delay(self, attempt_number: int) -> float:
        """Calculate delay before next retry"""
        if self.config.strategy == RetryStrategy.NO_RETRY:
            return 0
        
        elif self.config.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.config.base_delay
        
        elif self.config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.config.base_delay * attempt_number
        
        elif self.config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.config.base_delay * (self.config.backoff_multiplier ** (attempt_number - 1))
        
        elif self.config.strategy == RetryStrategy.FIBONACCI_BACKOFF:
            # Extend fibonacci sequence if needed
            while len(self.fibonacci_cache) < attempt_number:
                next_fib = self.fibonacci_cache[-1] + self.fibonacci_cache[-2]
                self.fibonacci_cache.append(next_fib)
            
            delay = self.config.base_delay * self.fibonacci_cache[attempt_number - 1]
        
        else:
            delay = self.config.base_delay
        
        # Apply max delay limit
        delay = min(delay, self.config.max_delay)
        
        # Add jitter to prevent thundering herd
        if self.config.jitter:
            jitter_amount = delay * 0.1 * random.random()
            delay += jitter_amount
        
        return delay


class ErrorRecoveryManager:
    """Manage error recovery strategies and fallback mechanisms"""
    
    def __init__(self):
        self.recovery_strategies = {
            ErrorType.BROWSER_ERROR: self._recover_browser_error,
            ErrorType.FACEBOOK_ERROR: self._recover_facebook_error,
            ErrorType.RATE_LIMIT_ERROR: self._recover_rate_limit_error,
            ErrorType.NETWORK_ERROR: self._recover_network_error,
            ErrorType.RESOURCE_ERROR: self._recover_resource_error
        }
        
        self.fallback_strategies = {
            ErrorType.BROWSER_ERROR: self._fallback_browser_error,
            ErrorType.FACEBOOK_ERROR: self._fallback_facebook_error,
            ErrorType.PARSING_ERROR: self._fallback_parsing_error
        }
    
    async def attempt_recovery(self, error_context: ErrorContext, scraper_service) -> bool:
        """Attempt to recover from an error"""
        try:
            recovery_func = self.recovery_strategies.get(error_context.error_type)
            if recovery_func:
                logger.info(f"Attempting recovery for {error_context.error_type.value}")
                return await recovery_func(error_context, scraper_service)
            return False
            
        except Exception as e:
            logger.error(f"Error during recovery attempt: {e}")
            return False
    
    async def apply_fallback(self, error_context: ErrorContext, scraper_service) -> Optional[Any]:
        """Apply fallback strategy when recovery fails"""
        try:
            fallback_func = self.fallback_strategies.get(error_context.error_type)
            if fallback_func:
                logger.info(f"Applying fallback for {error_context.error_type.value}")
                return await fallback_func(error_context, scraper_service)
            return None
            
        except Exception as e:
            logger.error(f"Error during fallback: {e}")
            return None
    
    async def _recover_browser_error(self, error_context: ErrorContext, scraper_service) -> bool:
        """Recover from browser-related errors"""
        try:
            profile_id = error_context.additional_info.get("profile_id")
            if profile_id:
                # Close and restart browser
                await scraper_service.browser_manager.close_browser(profile_id)
                await asyncio.sleep(2)
                browser = await scraper_service.browser_manager.launch_browser(profile_id)
                return browser is not None
            return False
        except Exception:
            return False
    
    async def _recover_facebook_error(self, error_context: ErrorContext, scraper_service) -> bool:
        """Recover from Facebook-specific errors"""
        try:
            profile_id = error_context.additional_info.get("profile_id")
            if profile_id:
                # Clear session and re-authenticate
                await scraper_service.session_handler.clear_session(profile_id)
                await asyncio.sleep(5)
                return True
            return False
        except Exception:
            return False
    
    async def _recover_rate_limit_error(self, error_context: ErrorContext, scraper_service) -> bool:
        """Recover from rate limiting"""
        try:
            # Wait longer for rate limit recovery
            wait_time = min(300, 60 * error_context.attempt_number)  # Max 5 minutes
            logger.info(f"Rate limit detected, waiting {wait_time} seconds")
            await asyncio.sleep(wait_time)
            return True
        except Exception:
            return False
    
    async def _recover_network_error(self, error_context: ErrorContext, scraper_service) -> bool:
        """Recover from network errors"""
        try:
            # Test network connectivity
            await asyncio.sleep(5)
            return True  # Assume network recovers
        except Exception:
            return False
    
    async def _recover_resource_error(self, error_context: ErrorContext, scraper_service) -> bool:
        """Recover from resource exhaustion"""
        try:
            # Force garbage collection and optimize memory
            import gc
            gc.collect()
            
            # Reduce concurrent operations
            if hasattr(scraper_service, 'parallel_processor'):
                await scraper_service.parallel_processor.auto_scale_workers()
            
            await asyncio.sleep(10)
            return True
        except Exception:
            return False
    
    async def _fallback_browser_error(self, error_context: ErrorContext, scraper_service) -> Optional[Any]:
        """Fallback for persistent browser errors"""
        # Try with different browser profile
        profile_id = error_context.additional_info.get("profile_id")
        if profile_id:
            fallback_profile = f"{profile_id}_fallback"
            logger.info(f"Using fallback profile: {fallback_profile}")
            return {"fallback_profile": fallback_profile}
        return None
    
    async def _fallback_facebook_error(self, error_context: ErrorContext, scraper_service) -> Optional[Any]:
        """Fallback for Facebook access issues"""
        # Return partial results or alternative data source
        return {"partial_results": True, "alternative_method": "basic_scraping"}
    
    async def _fallback_parsing_error(self, error_context: ErrorContext, scraper_service) -> Optional[Any]:
        """Fallback for parsing errors"""
        # Use alternative parsing method
        return {"alternative_parser": True, "reduced_accuracy": True}


class RobustErrorHandler:
    """Main error handler with comprehensive retry and recovery logic"""
    
    def __init__(self, retry_config: Optional[RetryConfig] = None):
        self.retry_config = retry_config or RetryConfig()
        self.retry_handler = RetryHandler(self.retry_config)
        self.recovery_manager = ErrorRecoveryManager()
        self.error_classifier = FacebookErrorClassifier()
        
        # Statistics
        self.error_stats = {
            "total_errors": 0,
            "errors_by_type": {},
            "successful_retries": 0,
            "failed_retries": 0,
            "recovery_attempts": 0,
            "successful_recoveries": 0
        }
    
    def with_retry(self, 
                   retry_config: Optional[RetryConfig] = None,
                   recovery_enabled: bool = True):
        """Decorator for adding retry logic to functions"""
        config = retry_config or self.retry_config
        
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                return await self.execute_with_retry(
                    func, args, kwargs, config, recovery_enabled
                )
            return wrapper
        return decorator
    
    async def execute_with_retry(self,
                               func: Callable,
                               args: tuple,
                               kwargs: dict,
                               retry_config: Optional[RetryConfig] = None,
                               recovery_enabled: bool = True) -> Any:
        """Execute function with retry logic"""
        config = retry_config or self.retry_config
        retry_handler = RetryHandler(config)
        
        start_time = time.time()
        last_exception = None
        
        for attempt in range(1, config.max_attempts + 1):
            try:
                # Execute the function
                if inspect.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Success - update stats and return
                if attempt > 1:
                    self.error_stats["successful_retries"] += 1
                    logger.info(f"Function {func.__name__} succeeded on attempt {attempt}")
                
                return result
                
            except Exception as e:
                last_exception = e
                self.error_stats["total_errors"] += 1
                
                # Classify error
                error_type = self.error_classifier.classify_error(e)
                self.error_stats["errors_by_type"][error_type.value] = (
                    self.error_stats["errors_by_type"].get(error_type.value, 0) + 1
                )
                
                # Create error context
                error_context = ErrorContext(
                    error_type=error_type,
                    original_exception=e,
                    function_name=func.__name__,
                    attempt_number=attempt,
                    total_attempts=config.max_attempts,
                    elapsed_time=time.time() - start_time,
                    additional_info=kwargs
                )
                
                logger.warning(f"Attempt {attempt}/{config.max_attempts} failed for {func.__name__}: {e}")
                
                # Check if we should retry
                if not retry_handler.should_retry(error_context):
                    logger.error(f"No more retries for {func.__name__} after {attempt} attempts")
                    break
                
                # Attempt recovery if enabled
                if recovery_enabled and attempt < config.max_attempts:
                    self.error_stats["recovery_attempts"] += 1
                    
                    # Get scraper service from args/kwargs
                    scraper_service = self._extract_scraper_service(args, kwargs)
                    if scraper_service:
                        recovery_success = await self.recovery_manager.attempt_recovery(
                            error_context, scraper_service
                        )
                        if recovery_success:
                            self.error_stats["successful_recoveries"] += 1
                            logger.info(f"Recovery successful for {func.__name__}")
                
                # Calculate delay before next attempt
                if attempt < config.max_attempts:
                    delay = retry_handler.calculate_delay(attempt)
                    if delay > 0:
                        logger.info(f"Waiting {delay:.1f}s before retry {attempt + 1}")
                        await asyncio.sleep(delay)
        
        # All retries failed
        self.error_stats["failed_retries"] += 1
        logger.error(f"All retries failed for {func.__name__}")
        
        # Try fallback strategy
        if recovery_enabled:
            scraper_service = self._extract_scraper_service(args, kwargs)
            if scraper_service:
                error_context = ErrorContext(
                    error_type=self.error_classifier.classify_error(last_exception),
                    original_exception=last_exception,
                    function_name=func.__name__,
                    attempt_number=config.max_attempts,
                    total_attempts=config.max_attempts,
                    elapsed_time=time.time() - start_time,
                    additional_info=kwargs
                )
                
                fallback_result = await self.recovery_manager.apply_fallback(
                    error_context, scraper_service
                )
                if fallback_result:
                    logger.info(f"Fallback strategy applied for {func.__name__}")
                    return fallback_result
        
        # Re-raise the last exception
        raise last_exception
    
    def _extract_scraper_service(self, args: tuple, kwargs: dict):
        """Extract scraper service from function arguments"""
        # Look for scraper service in args
        for arg in args:
            if hasattr(arg, 'browser_manager') and hasattr(arg, 'session_handler'):
                return arg
        
        # Look for scraper service in kwargs
        for value in kwargs.values():
            if hasattr(value, 'browser_manager') and hasattr(value, 'session_handler'):
                return value
        
        return None
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get comprehensive error statistics"""
        total_attempts = self.error_stats["successful_retries"] + self.error_stats["failed_retries"]
        
        return {
            "error_stats": self.error_stats,
            "success_rate": (
                self.error_stats["successful_retries"] / max(total_attempts, 1)
            ),
            "recovery_rate": (
                self.error_stats["successful_recoveries"] / 
                max(self.error_stats["recovery_attempts"], 1)
            ),
            "most_common_errors": sorted(
                self.error_stats["errors_by_type"].items(),
                key=lambda x: x[1],
                reverse=True
            )[:5]
        }
