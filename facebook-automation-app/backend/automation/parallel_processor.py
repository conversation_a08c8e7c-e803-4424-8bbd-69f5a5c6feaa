"""
Parallel Processing System for high-performance Facebook scraping
Handles concurrent browser instances, parallel UID extraction, and resource management
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Set, Callable, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
from loguru import logger
from dataclasses import dataclass
from enum import Enum
import multiprocessing as mp
from pathlib import Path
import json
import psutil
import threading
from queue import Queue, Empty


class ProcessingMode(Enum):
    SEQUENTIAL = "sequential"
    CONCURRENT = "concurrent"
    PARALLEL = "parallel"
    HYBRID = "hybrid"


@dataclass
class ProcessingTask:
    """Individual processing task"""
    task_id: str
    profile_id: str
    post_url: str
    priority: int = 1
    max_scroll_time: int = 300
    created_at: float = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()


@dataclass
class ProcessingResult:
    """Processing result with metadata"""
    task_id: str
    success: bool
    uids: List[str] = None
    error: str = None
    processing_time: float = 0
    memory_used: float = 0
    browser_stats: Dict[str, Any] = None


class ResourceMonitor:
    """Monitor system resources and adjust processing accordingly"""
    
    def __init__(self):
        self.cpu_threshold = 80.0  # CPU usage percentage
        self.memory_threshold = 85.0  # Memory usage percentage
        self.max_browsers = self._calculate_max_browsers()
        self.monitoring = False
        self.stats = {
            "cpu_usage": [],
            "memory_usage": [],
            "browser_count": [],
            "processing_rate": []
        }
    
    def _calculate_max_browsers(self) -> int:
        """Calculate optimal number of concurrent browsers based on system resources"""
        try:
            # Get system specs
            cpu_count = mp.cpu_count()
            memory_gb = psutil.virtual_memory().total / (1024**3)
            
            # Conservative calculation: 1 browser per 2 CPU cores, max 1 per 2GB RAM
            max_by_cpu = max(1, cpu_count // 2)
            max_by_memory = max(1, int(memory_gb // 2))
            
            optimal = min(max_by_cpu, max_by_memory, 8)  # Cap at 8 browsers
            logger.info(f"Calculated max browsers: {optimal} (CPU: {cpu_count}, RAM: {memory_gb:.1f}GB)")
            
            return optimal
            
        except Exception as e:
            logger.error(f"Error calculating max browsers: {e}")
            return 2  # Safe default
    
    def start_monitoring(self):
        """Start resource monitoring in background"""
        if not self.monitoring:
            self.monitoring = True
            threading.Thread(target=self._monitor_loop, daemon=True).start()
            logger.info("Resource monitoring started")
    
    def stop_monitoring(self):
        """Stop resource monitoring"""
        self.monitoring = False
        logger.info("Resource monitoring stopped")
    
    def _monitor_loop(self):
        """Background monitoring loop"""
        while self.monitoring:
            try:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                self.stats["cpu_usage"].append(cpu_percent)
                self.stats["memory_usage"].append(memory_percent)
                
                # Keep only last 60 readings (1 minute)
                for key in ["cpu_usage", "memory_usage"]:
                    if len(self.stats[key]) > 60:
                        self.stats[key] = self.stats[key][-60:]
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(5)
    
    def should_throttle(self) -> bool:
        """Check if processing should be throttled due to resource constraints"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            
            return (cpu_percent > self.cpu_threshold or 
                   memory_percent > self.memory_threshold)
                   
        except Exception:
            return False
    
    def get_optimal_concurrency(self) -> int:
        """Get optimal concurrency level based on current resources"""
        if self.should_throttle():
            return max(1, self.max_browsers // 2)
        return self.max_browsers
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """Get current resource statistics"""
        try:
            return {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent,
                "max_browsers": self.max_browsers,
                "optimal_concurrency": self.get_optimal_concurrency(),
                "should_throttle": self.should_throttle(),
                "historical_stats": {
                    "avg_cpu": sum(self.stats["cpu_usage"][-10:]) / max(len(self.stats["cpu_usage"][-10:]), 1),
                    "avg_memory": sum(self.stats["memory_usage"][-10:]) / max(len(self.stats["memory_usage"][-10:]), 1)
                }
            }
        except Exception as e:
            logger.error(f"Error getting resource stats: {e}")
            return {}


class ParallelProcessor:
    """High-performance parallel processing system for Facebook scraping"""
    
    def __init__(self, facebook_scraper_service, max_workers: Optional[int] = None):
        self.scraper_service = facebook_scraper_service
        self.resource_monitor = ResourceMonitor()
        
        # Processing configuration
        self.max_workers = max_workers or self.resource_monitor.max_browsers
        self.processing_mode = ProcessingMode.HYBRID
        
        # Task management
        self.task_queue = Queue()
        self.active_tasks: Dict[str, ProcessingTask] = {}
        self.completed_tasks: Dict[str, ProcessingResult] = {}
        
        # Executors
        self.thread_executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.process_executor = ProcessPoolExecutor(max_workers=min(4, mp.cpu_count()))
        
        # Statistics
        self.stats = {
            "tasks_submitted": 0,
            "tasks_completed": 0,
            "tasks_failed": 0,
            "total_processing_time": 0,
            "total_uids_extracted": 0,
            "start_time": time.time()
        }
        
        # Start monitoring
        self.resource_monitor.start_monitoring()
        logger.info(f"Parallel processor initialized with {self.max_workers} max workers")
    
    async def submit_task(self, task: ProcessingTask) -> str:
        """Submit a processing task"""
        try:
            self.task_queue.put(task)
            self.active_tasks[task.task_id] = task
            self.stats["tasks_submitted"] += 1
            
            logger.info(f"Task submitted: {task.task_id} for {task.post_url}")
            return task.task_id
            
        except Exception as e:
            logger.error(f"Error submitting task: {e}")
            raise
    
    async def submit_multiple_tasks(self, tasks: List[ProcessingTask]) -> List[str]:
        """Submit multiple tasks for parallel processing"""
        task_ids = []
        for task in tasks:
            task_id = await self.submit_task(task)
            task_ids.append(task_id)
        
        logger.info(f"Submitted {len(tasks)} tasks for parallel processing")
        return task_ids
    
    async def process_tasks_parallel(
        self, 
        max_concurrent: Optional[int] = None,
        progress_callback: Optional[Callable] = None
    ) -> List[ProcessingResult]:
        """Process all queued tasks in parallel"""
        try:
            max_concurrent = max_concurrent or self.resource_monitor.get_optimal_concurrency()
            logger.info(f"Starting parallel processing with {max_concurrent} concurrent workers")
            
            # Get all tasks from queue
            tasks_to_process = []
            while not self.task_queue.empty():
                try:
                    task = self.task_queue.get_nowait()
                    tasks_to_process.append(task)
                except Empty:
                    break
            
            if not tasks_to_process:
                logger.info("No tasks to process")
                return []
            
            # Create semaphore to limit concurrency
            semaphore = asyncio.Semaphore(max_concurrent)
            
            # Process tasks concurrently
            async def process_single_task(task: ProcessingTask) -> ProcessingResult:
                async with semaphore:
                    return await self._process_task_with_monitoring(task, progress_callback)
            
            # Execute all tasks
            results = await asyncio.gather(
                *[process_single_task(task) for task in tasks_to_process],
                return_exceptions=True
            )
            
            # Process results
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    error_result = ProcessingResult(
                        task_id=tasks_to_process[i].task_id,
                        success=False,
                        error=str(result)
                    )
                    processed_results.append(error_result)
                    self.stats["tasks_failed"] += 1
                else:
                    processed_results.append(result)
                    if result.success:
                        self.stats["tasks_completed"] += 1
                        self.stats["total_uids_extracted"] += len(result.uids or [])
                    else:
                        self.stats["tasks_failed"] += 1
                
                # Store result
                self.completed_tasks[tasks_to_process[i].task_id] = processed_results[-1]
            
            logger.info(f"Parallel processing completed: {len(processed_results)} tasks processed")
            return processed_results
            
        except Exception as e:
            logger.error(f"Error in parallel processing: {e}")
            raise
    
    async def _process_task_with_monitoring(
        self, 
        task: ProcessingTask,
        progress_callback: Optional[Callable] = None
    ) -> ProcessingResult:
        """Process single task with resource monitoring"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        try:
            logger.info(f"Processing task {task.task_id}: {task.post_url}")
            
            # Check resource constraints
            if self.resource_monitor.should_throttle():
                logger.warning(f"Resource throttling active for task {task.task_id}")
                await asyncio.sleep(2)  # Brief delay
            
            # Progress callback
            if progress_callback:
                await progress_callback({
                    "task_id": task.task_id,
                    "status": "started",
                    "timestamp": time.time()
                })
            
            # Execute scraping
            scraping_result = await self.scraper_service.scrape_facebook_post_uids(
                profile_id=task.profile_id,
                post_url=task.post_url,
                max_scroll_time=task.max_scroll_time,
                progress_callback=progress_callback
            )
            
            # Calculate metrics
            processing_time = time.time() - start_time
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            memory_used = end_memory - start_memory
            
            # Create result
            if scraping_result["success"]:
                result = ProcessingResult(
                    task_id=task.task_id,
                    success=True,
                    uids=scraping_result["results"]["new_uids"],
                    processing_time=processing_time,
                    memory_used=memory_used,
                    browser_stats=scraping_result["results"].get("scrolling_stats", {})
                )
                logger.info(f"Task {task.task_id} completed: {len(result.uids)} UIDs in {processing_time:.1f}s")
            else:
                result = ProcessingResult(
                    task_id=task.task_id,
                    success=False,
                    error=scraping_result.get("error", "Unknown error"),
                    processing_time=processing_time,
                    memory_used=memory_used
                )
                logger.error(f"Task {task.task_id} failed: {result.error}")
            
            # Progress callback
            if progress_callback:
                await progress_callback({
                    "task_id": task.task_id,
                    "status": "completed" if result.success else "failed",
                    "result": result,
                    "timestamp": time.time()
                })
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error processing task {task.task_id}: {e}")
            
            return ProcessingResult(
                task_id=task.task_id,
                success=False,
                error=str(e),
                processing_time=processing_time
            )
        finally:
            # Clean up active task
            self.active_tasks.pop(task.task_id, None)
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics"""
        runtime = time.time() - self.stats["start_time"]
        
        return {
            "processing_stats": {
                **self.stats,
                "runtime_seconds": runtime,
                "processing_rate": self.stats["tasks_completed"] / max(runtime, 1),
                "success_rate": (
                    self.stats["tasks_completed"] / 
                    max(self.stats["tasks_submitted"], 1)
                ),
                "active_tasks": len(self.active_tasks),
                "completed_tasks": len(self.completed_tasks),
                "queue_size": self.task_queue.qsize()
            },
            "resource_stats": self.resource_monitor.get_resource_stats(),
            "performance_metrics": {
                "avg_processing_time": (
                    self.stats["total_processing_time"] / 
                    max(self.stats["tasks_completed"], 1)
                ),
                "uids_per_second": (
                    self.stats["total_uids_extracted"] / max(runtime, 1)
                )
            }
        }
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get status of specific task"""
        if task_id in self.completed_tasks:
            result = self.completed_tasks[task_id]
            return {
                "status": "completed",
                "success": result.success,
                "result": result
            }
        elif task_id in self.active_tasks:
            return {
                "status": "processing",
                "task": self.active_tasks[task_id]
            }
        else:
            return {
                "status": "not_found"
            }
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("Cleaning up parallel processor...")
            
            # Stop monitoring
            self.resource_monitor.stop_monitoring()
            
            # Shutdown executors
            self.thread_executor.shutdown(wait=True)
            self.process_executor.shutdown(wait=True)
            
            logger.info("Parallel processor cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def optimize_processing_mode(self) -> ProcessingMode:
        """Dynamically optimize processing mode based on current conditions"""
        resource_stats = self.resource_monitor.get_resource_stats()
        
        cpu_percent = resource_stats.get("cpu_percent", 0)
        memory_percent = resource_stats.get("memory_percent", 0)
        
        if cpu_percent > 90 or memory_percent > 90:
            return ProcessingMode.SEQUENTIAL
        elif cpu_percent > 70 or memory_percent > 70:
            return ProcessingMode.CONCURRENT
        else:
            return ProcessingMode.PARALLEL
    
    async def auto_scale_workers(self):
        """Automatically scale number of workers based on performance"""
        try:
            optimal_concurrency = self.resource_monitor.get_optimal_concurrency()
            
            if optimal_concurrency != self.max_workers:
                logger.info(f"Auto-scaling workers: {self.max_workers} -> {optimal_concurrency}")
                self.max_workers = optimal_concurrency
                
                # Recreate thread executor with new size
                self.thread_executor.shutdown(wait=False)
                self.thread_executor = ThreadPoolExecutor(max_workers=self.max_workers)
                
        except Exception as e:
            logger.error(f"Error in auto-scaling: {e}")
