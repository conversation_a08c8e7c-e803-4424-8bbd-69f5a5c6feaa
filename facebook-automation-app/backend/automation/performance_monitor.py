"""
Performance Monitoring and Metrics Collection System
Comprehensive performance tracking, metrics collection, and analytics for Facebook scraping
"""

import time
import asyncio
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass, field
from collections import deque, defaultdict
from loguru import logger
import json
import statistics
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import weakref


@dataclass
class PerformanceMetric:
    """Individual performance metric"""
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OperationStats:
    """Statistics for a specific operation"""
    operation_name: str
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    total_duration: float = 0.0
    min_duration: float = float('inf')
    max_duration: float = 0.0
    avg_duration: float = 0.0
    last_execution: Optional[float] = None
    error_rate: float = 0.0
    throughput: float = 0.0  # operations per second


@dataclass
class SystemMetrics:
    """System-level performance metrics"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_threads: int
    timestamp: float


class MetricsCollector:
    """Collect and aggregate performance metrics"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history = deque(maxlen=max_history)
        self.operation_stats = defaultdict(OperationStats)
        self.system_metrics_history = deque(maxlen=max_history)
        self.custom_metrics = defaultdict(list)
        
        # Performance counters
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.timers = defaultdict(list)
        
        # System monitoring
        self.last_disk_io = None
        self.last_network_io = None
        self.monitoring_active = False
        
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None, metadata: Dict[str, Any] = None):
        """Record a performance metric"""
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=time.time(),
            tags=tags or {},
            metadata=metadata or {}
        )
        self.metrics_history.append(metric)
        
        # Update custom metrics
        self.custom_metrics[name].append(value)
        if len(self.custom_metrics[name]) > self.max_history:
            self.custom_metrics[name] = self.custom_metrics[name][-self.max_history:]
    
    def increment_counter(self, name: str, value: int = 1, tags: Dict[str, str] = None):
        """Increment a counter metric"""
        self.counters[name] += value
        self.record_metric(f"counter.{name}", self.counters[name], tags)
    
    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """Set a gauge metric"""
        self.gauges[name] = value
        self.record_metric(f"gauge.{name}", value, tags)
    
    def record_timer(self, name: str, duration: float, tags: Dict[str, str] = None):
        """Record a timer metric"""
        self.timers[name].append(duration)
        if len(self.timers[name]) > self.max_history:
            self.timers[name] = self.timers[name][-self.max_history:]
        
        self.record_metric(f"timer.{name}", duration, tags)
    
    def record_operation(self, operation_name: str, duration: float, success: bool = True):
        """Record operation performance"""
        stats = self.operation_stats[operation_name]
        stats.operation_name = operation_name
        stats.total_executions += 1
        stats.total_duration += duration
        stats.last_execution = time.time()
        
        if success:
            stats.successful_executions += 1
        else:
            stats.failed_executions += 1
        
        # Update min/max duration
        stats.min_duration = min(stats.min_duration, duration)
        stats.max_duration = max(stats.max_duration, duration)
        
        # Calculate averages
        stats.avg_duration = stats.total_duration / stats.total_executions
        stats.error_rate = stats.failed_executions / stats.total_executions
        
        # Calculate throughput (operations per second over last minute)
        recent_ops = [op for op in self.metrics_history 
                     if op.name == f"operation.{operation_name}" and 
                     time.time() - op.timestamp <= 60]
        stats.throughput = len(recent_ops) / 60.0
        
        # Record as metric
        self.record_metric(
            f"operation.{operation_name}",
            duration,
            tags={"success": str(success)},
            metadata={"operation_stats": stats}
        )
    
    def collect_system_metrics(self) -> SystemMetrics:
        """Collect current system metrics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            disk_read_mb = 0
            disk_write_mb = 0
            
            if disk_io and self.last_disk_io:
                disk_read_mb = (disk_io.read_bytes - self.last_disk_io.read_bytes) / 1024 / 1024
                disk_write_mb = (disk_io.write_bytes - self.last_disk_io.write_bytes) / 1024 / 1024
            
            self.last_disk_io = disk_io
            
            # Network I/O
            network_io = psutil.net_io_counters()
            network_sent_mb = 0
            network_recv_mb = 0
            
            if network_io and self.last_network_io:
                network_sent_mb = (network_io.bytes_sent - self.last_network_io.bytes_sent) / 1024 / 1024
                network_recv_mb = (network_io.bytes_recv - self.last_network_io.bytes_recv) / 1024 / 1024
            
            self.last_network_io = network_io
            
            # Thread count
            active_threads = threading.active_count()
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                active_threads=active_threads,
                timestamp=time.time()
            )
            
            self.system_metrics_history.append(metrics)
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            return SystemMetrics(0, 0, 0, 0, 0, 0, 0, 0, time.time())
    
    def get_operation_summary(self, operation_name: str = None) -> Dict[str, Any]:
        """Get summary of operation performance"""
        if operation_name:
            stats = self.operation_stats.get(operation_name)
            if not stats:
                return {}
            
            return {
                "operation_name": stats.operation_name,
                "total_executions": stats.total_executions,
                "success_rate": (stats.successful_executions / max(stats.total_executions, 1)) * 100,
                "error_rate": stats.error_rate * 100,
                "avg_duration_ms": stats.avg_duration * 1000,
                "min_duration_ms": stats.min_duration * 1000 if stats.min_duration != float('inf') else 0,
                "max_duration_ms": stats.max_duration * 1000,
                "throughput_ops_per_sec": stats.throughput,
                "last_execution": stats.last_execution
            }
        else:
            # Return summary for all operations
            return {
                op_name: self.get_operation_summary(op_name)
                for op_name in self.operation_stats.keys()
            }
    
    def get_metrics_summary(self, time_window: int = 300) -> Dict[str, Any]:
        """Get summary of metrics within time window (seconds)"""
        cutoff_time = time.time() - time_window
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        if not recent_metrics:
            return {}
        
        # Group metrics by name
        metrics_by_name = defaultdict(list)
        for metric in recent_metrics:
            metrics_by_name[metric.name].append(metric.value)
        
        # Calculate statistics for each metric
        summary = {}
        for name, values in metrics_by_name.items():
            if values:
                summary[name] = {
                    "count": len(values),
                    "min": min(values),
                    "max": max(values),
                    "avg": statistics.mean(values),
                    "median": statistics.median(values),
                    "std_dev": statistics.stdev(values) if len(values) > 1 else 0
                }
        
        return summary


class PerformanceProfiler:
    """Profile performance of specific operations"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.active_profiles = {}
        
    @asynccontextmanager
    async def profile_operation(self, operation_name: str, tags: Dict[str, str] = None):
        """Context manager for profiling operations"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        profile_id = f"{operation_name}_{int(start_time)}"
        self.active_profiles[profile_id] = {
            "operation_name": operation_name,
            "start_time": start_time,
            "start_memory": start_memory,
            "tags": tags or {}
        }
        
        success = True
        error = None
        
        try:
            yield profile_id
        except Exception as e:
            success = False
            error = str(e)
            raise
        finally:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            duration = end_time - start_time
            memory_delta = end_memory - start_memory
            
            # Record metrics
            self.metrics_collector.record_operation(operation_name, duration, success)
            self.metrics_collector.record_metric(
                f"memory_delta.{operation_name}",
                memory_delta,
                tags,
                metadata={"success": success, "error": error}
            )
            
            # Clean up
            self.active_profiles.pop(profile_id, None)
            
            logger.debug(
                f"Operation {operation_name} completed in {duration:.3f}s "
                f"(memory: {memory_delta:+.1f}MB, success: {success})"
            )


class PerformanceAnalyzer:
    """Analyze performance trends and detect anomalies"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.baseline_metrics = {}
        self.anomaly_thresholds = {
            "cpu_percent": 80.0,
            "memory_percent": 85.0,
            "error_rate": 0.05,  # 5%
            "response_time_multiplier": 3.0  # 3x normal response time
        }
    
    def establish_baseline(self, time_window: int = 3600):
        """Establish performance baseline from historical data"""
        cutoff_time = time.time() - time_window
        recent_metrics = [m for m in self.metrics_collector.metrics_history 
                         if m.timestamp >= cutoff_time]
        
        if not recent_metrics:
            logger.warning("No metrics available for baseline establishment")
            return
        
        # Group metrics by name
        metrics_by_name = defaultdict(list)
        for metric in recent_metrics:
            metrics_by_name[metric.name].append(metric.value)
        
        # Calculate baseline statistics
        for name, values in metrics_by_name.items():
            if len(values) >= 10:  # Need sufficient data points
                self.baseline_metrics[name] = {
                    "mean": statistics.mean(values),
                    "std_dev": statistics.stdev(values) if len(values) > 1 else 0,
                    "median": statistics.median(values),
                    "p95": sorted(values)[int(len(values) * 0.95)],
                    "sample_size": len(values)
                }
        
        logger.info(f"Established baseline for {len(self.baseline_metrics)} metrics")
    
    def detect_anomalies(self, time_window: int = 300) -> List[Dict[str, Any]]:
        """Detect performance anomalies"""
        anomalies = []
        
        # Check system metrics
        if self.metrics_collector.system_metrics_history:
            latest_system = self.metrics_collector.system_metrics_history[-1]
            
            if latest_system.cpu_percent > self.anomaly_thresholds["cpu_percent"]:
                anomalies.append({
                    "type": "high_cpu",
                    "value": latest_system.cpu_percent,
                    "threshold": self.anomaly_thresholds["cpu_percent"],
                    "timestamp": latest_system.timestamp
                })
            
            if latest_system.memory_percent > self.anomaly_thresholds["memory_percent"]:
                anomalies.append({
                    "type": "high_memory",
                    "value": latest_system.memory_percent,
                    "threshold": self.anomaly_thresholds["memory_percent"],
                    "timestamp": latest_system.timestamp
                })
        
        # Check operation error rates
        for op_name, stats in self.metrics_collector.operation_stats.items():
            if stats.error_rate > self.anomaly_thresholds["error_rate"]:
                anomalies.append({
                    "type": "high_error_rate",
                    "operation": op_name,
                    "value": stats.error_rate,
                    "threshold": self.anomaly_thresholds["error_rate"],
                    "timestamp": time.time()
                })
        
        # Check response time anomalies against baseline
        for metric_name, baseline in self.baseline_metrics.items():
            if "timer." in metric_name:
                recent_values = self.metrics_collector.custom_metrics.get(metric_name, [])
                if recent_values:
                    latest_value = recent_values[-1]
                    if latest_value > baseline["mean"] * self.anomaly_thresholds["response_time_multiplier"]:
                        anomalies.append({
                            "type": "slow_response",
                            "metric": metric_name,
                            "value": latest_value,
                            "baseline_mean": baseline["mean"],
                            "multiplier": latest_value / baseline["mean"],
                            "timestamp": time.time()
                        })
        
        return anomalies
    
    def get_performance_trends(self, time_window: int = 3600) -> Dict[str, Any]:
        """Analyze performance trends"""
        cutoff_time = time.time() - time_window
        
        # System metrics trend
        recent_system_metrics = [m for m in self.metrics_collector.system_metrics_history 
                               if m.timestamp >= cutoff_time]
        
        trends = {}
        
        if len(recent_system_metrics) >= 2:
            # Calculate trends for system metrics
            cpu_values = [m.cpu_percent for m in recent_system_metrics]
            memory_values = [m.memory_percent for m in recent_system_metrics]
            
            trends["system"] = {
                "cpu_trend": self._calculate_trend(cpu_values),
                "memory_trend": self._calculate_trend(memory_values),
                "sample_size": len(recent_system_metrics)
            }
        
        # Operation performance trends
        operation_trends = {}
        for op_name, stats in self.metrics_collector.operation_stats.items():
            if stats.total_executions >= 10:
                # Get recent operation metrics
                recent_ops = [m for m in self.metrics_collector.metrics_history 
                             if m.name == f"operation.{op_name}" and m.timestamp >= cutoff_time]
                
                if len(recent_ops) >= 5:
                    durations = [m.value for m in recent_ops]
                    operation_trends[op_name] = {
                        "response_time_trend": self._calculate_trend(durations),
                        "throughput": len(recent_ops) / (time_window / 3600),  # ops per hour
                        "sample_size": len(recent_ops)
                    }
        
        trends["operations"] = operation_trends
        return trends
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction from values"""
        if len(values) < 2:
            return "insufficient_data"
        
        # Simple linear trend calculation
        n = len(values)
        x_sum = sum(range(n))
        y_sum = sum(values)
        xy_sum = sum(i * values[i] for i in range(n))
        x2_sum = sum(i * i for i in range(n))
        
        slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum)
        
        if abs(slope) < 0.01:  # Threshold for "stable"
            return "stable"
        elif slope > 0:
            return "increasing"
        else:
            return "decreasing"


class PerformanceMonitor:
    """Main performance monitoring system"""
    
    def __init__(self, collection_interval: float = 30.0):
        self.collection_interval = collection_interval
        self.metrics_collector = MetricsCollector()
        self.profiler = PerformanceProfiler(self.metrics_collector)
        self.analyzer = PerformanceAnalyzer(self.metrics_collector)
        
        self.monitoring_active = False
        self.monitoring_task = None
        
        # Callbacks for anomalies
        self.anomaly_callbacks = []
        
    def add_anomaly_callback(self, callback: Callable):
        """Add callback for anomaly detection"""
        self.anomaly_callbacks.append(callback)
    
    async def start_monitoring(self):
        """Start performance monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop performance monitoring"""
        if self.monitoring_active:
            self.monitoring_active = False
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            logger.info("Performance monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Collect system metrics
                system_metrics = self.metrics_collector.collect_system_metrics()
                
                # Record system metrics as individual metrics
                self.metrics_collector.record_metric("system.cpu_percent", system_metrics.cpu_percent)
                self.metrics_collector.record_metric("system.memory_percent", system_metrics.memory_percent)
                self.metrics_collector.record_metric("system.memory_used_mb", system_metrics.memory_used_mb)
                self.metrics_collector.record_metric("system.active_threads", system_metrics.active_threads)
                
                # Check for anomalies
                anomalies = self.analyzer.detect_anomalies()
                if anomalies:
                    for callback in self.anomaly_callbacks:
                        try:
                            await callback(anomalies)
                        except Exception as e:
                            logger.error(f"Error in anomaly callback: {e}")
                
                await asyncio.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.collection_interval)
    
    def get_comprehensive_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        return {
            "timestamp": time.time(),
            "system_metrics": self.metrics_collector.system_metrics_history[-1].__dict__ if self.metrics_collector.system_metrics_history else {},
            "operation_summary": self.metrics_collector.get_operation_summary(),
            "metrics_summary": self.metrics_collector.get_metrics_summary(),
            "performance_trends": self.analyzer.get_performance_trends(),
            "recent_anomalies": self.analyzer.detect_anomalies(),
            "baseline_established": bool(self.analyzer.baseline_metrics),
            "monitoring_active": self.monitoring_active
        }
    
    async def profile_operation(self, operation_name: str, tags: Dict[str, str] = None):
        """Get profiler context manager"""
        return self.profiler.profile_operation(operation_name, tags)
    
    def record_custom_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """Record custom metric"""
        self.metrics_collector.record_metric(name, value, tags)
    
    def increment_counter(self, name: str, value: int = 1):
        """Increment counter"""
        self.metrics_collector.increment_counter(name, value)
    
    def set_gauge(self, name: str, value: float):
        """Set gauge value"""
        self.metrics_collector.set_gauge(name, value)
    
    async def cleanup(self):
        """Cleanup performance monitor"""
        await self.stop_monitoring()
        logger.info("Performance monitor cleanup completed")
