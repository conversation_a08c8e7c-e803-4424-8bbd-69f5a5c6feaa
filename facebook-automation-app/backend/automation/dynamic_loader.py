"""
Dynamic Content Loading Handler for Facebook lazy loading, 'View more' buttons and dynamic content
"""

import asyncio
import random
import time
from typing import Dict, Any, List, Optional, Set
from loguru import logger

from zendriver import Browser


class DynamicContentLoader:
    """Handles dynamic content loading on Facebook pages"""
    
    def __init__(self):
        self.loading_stats = {
            "buttons_clicked": 0,
            "content_expansions": 0,
            "lazy_loads_triggered": 0,
            "total_wait_time": 0,
            "start_time": None,
            "end_time": None
        }
        
        # Button selectors for different types of "load more" buttons
        self.load_more_selectors = [
            # Standard "View more comments" buttons
            'div[role="button"]:has-text("View more comments")',
            'span:has-text("View more comments")',
            'a:has-text("View more comments")',
            
            # "See more" buttons
            'div[role="button"]:has-text("See more")',
            'span:has-text("See more")',
            'a:has-text("See more")',
            
            # "Load more" variations
            'div[role="button"]:has-text("Load more")',
            'button:has-text("Load more")',
            
            # "Show more" variations
            'div[role="button"]:has-text("Show more")',
            'span:has-text("Show more")',
            
            # Generic selectors for comment loading
            '[data-testid="UFI2CommentsCount/root"] div[role="button"]',
            '[data-testid="comment-replies-expand-button"]',
            
            # CSS class-based selectors
            '.UFIPagerLink',
            '.UFICommentLink',
            '.see_more_link',
            '.uiLinkSubtle[role="button"]',
            
            # Aria-label based selectors
            '[aria-label*="View more"]',
            '[aria-label*="See more"]',
            '[aria-label*="Load more"]',
            '[aria-label*="Show more"]',
        ]
        
        # Selectors for detecting loading states
        self.loading_indicators = [
            '.uiMorePagerPrimary',
            '.UFISpinner',
            '[role="progressbar"]',
            '.loading',
            '.spinner',
            '[data-testid="loading"]'
        ]
    
    async def load_all_dynamic_content(
        self, 
        browser: Browser, 
        max_iterations: int = 50,
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """
        Load all dynamic content by clicking load more buttons and handling lazy loading
        
        Args:
            browser: Zendriver browser instance
            max_iterations: Maximum number of load more attempts
            progress_callback: Function to report progress
        """
        try:
            page = await browser.get_current_page()
            if not page:
                return {"success": False, "error": "No active page found"}
            
            # Initialize stats
            self.loading_stats = {
                "buttons_clicked": 0,
                "content_expansions": 0,
                "lazy_loads_triggered": 0,
                "total_wait_time": 0,
                "start_time": time.time(),
                "end_time": None
            }
            
            logger.info("Starting dynamic content loading")
            
            iteration = 0
            consecutive_no_buttons = 0
            
            while iteration < max_iterations:
                iteration += 1
                
                # Find and click load more buttons
                buttons_found = await self._find_and_click_load_more_buttons(page)
                
                if buttons_found > 0:
                    self.loading_stats["buttons_clicked"] += buttons_found
                    consecutive_no_buttons = 0
                    
                    # Wait for content to load
                    await self._wait_for_content_loading(page)
                    
                    # Trigger lazy loading
                    lazy_loads = await self._trigger_lazy_loading(page)
                    self.loading_stats["lazy_loads_triggered"] += lazy_loads
                    
                    if progress_callback:
                        await progress_callback({
                            "iteration": iteration,
                            "buttons_clicked": self.loading_stats["buttons_clicked"],
                            "lazy_loads": self.loading_stats["lazy_loads_triggered"]
                        })
                else:
                    consecutive_no_buttons += 1
                    
                    # If no buttons found for 3 consecutive attempts, try other methods
                    if consecutive_no_buttons >= 3:
                        # Try expanding collapsed content
                        expansions = await self._expand_collapsed_content(page)
                        self.loading_stats["content_expansions"] += expansions
                        
                        if expansions == 0:
                            logger.info("No more dynamic content to load")
                            break
                        else:
                            consecutive_no_buttons = 0
                
                # Small delay between iterations
                await asyncio.sleep(random.uniform(1.0, 2.0))
            
            # Finalize stats
            self.loading_stats["end_time"] = time.time()
            
            logger.info(f"Dynamic content loading completed. Stats: {self.loading_stats}")
            
            return {
                "success": True,
                "stats": self.loading_stats,
                "iterations": iteration,
                "message": "Dynamic content loading completed"
            }
            
        except Exception as e:
            logger.error(f"Error loading dynamic content: {e}")
            return {
                "success": False,
                "error": str(e),
                "stats": self.loading_stats
            }
    
    async def _find_and_click_load_more_buttons(self, page) -> int:
        """Find and click all available load more buttons"""
        buttons_clicked = 0
        
        try:
            for selector in self.load_more_selectors:
                try:
                    # Find buttons with this selector
                    buttons = await page.find_elements("css selector", selector)
                    
                    for button in buttons:
                        try:
                            # Check if button is visible and clickable
                            is_visible = await button.is_displayed()
                            is_enabled = await button.is_enabled()
                            
                            if is_visible and is_enabled:
                                # Scroll button into view
                                await page.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                                await asyncio.sleep(0.5)
                                
                                # Click the button
                                await button.click()
                                buttons_clicked += 1
                                
                                logger.debug(f"Clicked load more button: {selector}")
                                
                                # Wait a bit after clicking
                                await asyncio.sleep(random.uniform(1.0, 2.0))
                                
                        except Exception as e:
                            logger.debug(f"Error clicking button: {e}")
                            continue
                            
                except Exception as e:
                    logger.debug(f"Error with selector {selector}: {e}")
                    continue
            
            return buttons_clicked
            
        except Exception as e:
            logger.error(f"Error finding load more buttons: {e}")
            return 0
    
    async def _wait_for_content_loading(self, page, max_wait: int = 10):
        """Wait for content to finish loading"""
        try:
            start_time = time.time()
            
            # Wait for loading indicators to appear and disappear
            for indicator in self.loading_indicators:
                try:
                    # Wait for indicator to appear (if it does)
                    await asyncio.sleep(0.5)
                    
                    # Wait for indicator to disappear
                    wait_time = 0
                    while wait_time < max_wait:
                        try:
                            elements = await page.find_elements("css selector", indicator)
                            if not elements:
                                break
                            
                            # Check if any loading indicators are still visible
                            visible_indicators = 0
                            for element in elements:
                                if await element.is_displayed():
                                    visible_indicators += 1
                            
                            if visible_indicators == 0:
                                break
                                
                        except:
                            break
                        
                        await asyncio.sleep(0.5)
                        wait_time += 0.5
                        
                except Exception:
                    continue
            
            # Additional wait for content to stabilize
            await asyncio.sleep(random.uniform(1.0, 2.0))
            
            total_wait = time.time() - start_time
            self.loading_stats["total_wait_time"] += total_wait
            
        except Exception as e:
            logger.error(f"Error waiting for content loading: {e}")
    
    async def _trigger_lazy_loading(self, page) -> int:
        """Trigger lazy loading by scrolling to lazy-loaded elements"""
        lazy_loads = 0
        
        try:
            # Find elements that might trigger lazy loading
            lazy_selectors = [
                'img[data-src]',  # Lazy loaded images
                '[data-lazy]',    # Generic lazy loading
                '.lazyload',      # Lazy loading class
                '[loading="lazy"]' # HTML5 lazy loading
            ]
            
            for selector in lazy_selectors:
                try:
                    elements = await page.find_elements("css selector", selector)
                    
                    for element in elements[:5]:  # Limit to first 5 to avoid too much scrolling
                        try:
                            # Scroll element into view to trigger lazy loading
                            await page.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                            await asyncio.sleep(0.3)
                            lazy_loads += 1
                            
                        except Exception:
                            continue
                            
                except Exception:
                    continue
            
            return lazy_loads
            
        except Exception as e:
            logger.error(f"Error triggering lazy loading: {e}")
            return 0
    
    async def _expand_collapsed_content(self, page) -> int:
        """Expand collapsed content sections"""
        expansions = 0
        
        try:
            # Selectors for collapsed content
            collapse_selectors = [
                # "See more" for post content
                'div[data-testid="post_message"] span:has-text("See more")',
                'div[data-testid="post_message"] span:has-text("See More")',
                
                # Comment "See more" links
                'span:has-text("See more")',
                'a:has-text("See more")',
                
                # Generic expand buttons
                '[aria-expanded="false"]',
                '.text_exposed_link',
                '.see_more_link',
                
                # Reply expansion
                'div[role="button"]:has-text("View")',
                'span:has-text("View replies")',
                'a:has-text("View replies")',
            ]
            
            for selector in collapse_selectors:
                try:
                    elements = await page.find_elements("css selector", selector)
                    
                    for element in elements:
                        try:
                            is_visible = await element.is_displayed()
                            is_enabled = await element.is_enabled()
                            
                            if is_visible and is_enabled:
                                # Scroll into view and click
                                await page.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                                await asyncio.sleep(0.5)
                                
                                await element.click()
                                expansions += 1
                                
                                # Wait for expansion
                                await asyncio.sleep(random.uniform(0.5, 1.0))
                                
                        except Exception:
                            continue
                            
                except Exception:
                    continue
            
            return expansions
            
        except Exception as e:
            logger.error(f"Error expanding collapsed content: {e}")
            return 0
    
    async def handle_infinite_scroll_with_loading(
        self, 
        browser: Browser,
        scroll_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """Handle infinite scroll combined with dynamic loading"""
        try:
            page = await browser.get_current_page()
            if not page:
                return {"success": False, "error": "No active page found"}
            
            # Combine scrolling with dynamic loading
            total_content_loaded = 0
            scroll_iterations = 0
            
            while scroll_iterations < 20:  # Limit iterations
                # Perform some scrolling
                if scroll_callback:
                    await scroll_callback(page)
                else:
                    await page.evaluate("window.scrollBy(0, 500);")
                
                await asyncio.sleep(1)
                
                # Load dynamic content
                result = await self.load_all_dynamic_content(browser, max_iterations=5)
                
                if result["success"]:
                    total_content_loaded += result["stats"]["buttons_clicked"]
                
                scroll_iterations += 1
                
                # Check if no more content is being loaded
                if result["stats"]["buttons_clicked"] == 0:
                    break
            
            return {
                "success": True,
                "total_content_loaded": total_content_loaded,
                "scroll_iterations": scroll_iterations,
                "message": "Infinite scroll with loading completed"
            }
            
        except Exception as e:
            logger.error(f"Error in infinite scroll with loading: {e}")
            return {"success": False, "error": str(e)}
    
    def get_loading_statistics(self) -> Dict[str, Any]:
        """Get detailed loading statistics"""
        if not self.loading_stats.get("start_time"):
            return {"error": "No loading session found"}
        
        duration = (self.loading_stats.get("end_time", time.time()) - 
                   self.loading_stats["start_time"])
        
        return {
            "buttons_clicked": self.loading_stats["buttons_clicked"],
            "content_expansions": self.loading_stats["content_expansions"],
            "lazy_loads_triggered": self.loading_stats["lazy_loads_triggered"],
            "total_wait_time": self.loading_stats["total_wait_time"],
            "duration_seconds": duration,
            "loading_efficiency": (
                self.loading_stats["buttons_clicked"] / max(duration, 1)
            )
        }
