"""
Integrated Facebook Scraper Service with complete workflow
Combines all components: <PERSON>rowser Manager, <PERSON> Handler, UID Extractor, Smart Scroller, Dynamic Loader, Deduplication
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Set, Callable
from loguru import logger
from pathlib import Path

from .browser_manager import Browser<PERSON>anager
from .facebook_session import <PERSON><PERSON>ession<PERSON>andler
from .uid_extractor import FacebookUIDExtractor
from .smart_scroller import SmartScrollingEngine
from .dynamic_loader import DynamicContentLoader
from .deduplication_system import UIDDeduplicationSystem


class FacebookScraperService:
    """Complete Facebook scraping service with all advanced features"""
    
    def __init__(self, persistence_dir: str = "scraping_data"):
        # Initialize all components
        self.browser_manager = BrowserManager()
        self.session_handler = FacebookSessionHandler(self.browser_manager)
        self.uid_extractor = FacebookUIDExtractor()
        self.smart_scroller = SmartScrollingEngine()
        self.dynamic_loader = DynamicContentLoader()
        
        # Setup persistence
        self.persistence_dir = Path(persistence_dir)
        self.persistence_dir.mkdir(exist_ok=True)
        
        # Initialize deduplication system with persistence
        dedup_file = self.persistence_dir / "uid_deduplication.json"
        self.deduplication_system = UIDDeduplicationSystem(
            max_memory_uids=50000,
            persistence_file=str(dedup_file)
        )
        
        # Scraping statistics
        self.scraping_stats = {
            "sessions_created": 0,
            "pages_scraped": 0,
            "total_uids_extracted": 0,
            "unique_uids_found": 0,
            "total_scraping_time": 0,
            "start_time": None
        }
    
    async def scrape_facebook_post_uids(
        self,
        profile_id: str,
        post_url: str,
        max_scroll_time: int = 300,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Complete workflow to scrape UIDs from Facebook post
        
        Args:
            profile_id: Browser profile ID to use
            post_url: Facebook post URL to scrape
            max_scroll_time: Maximum time to spend scrolling (seconds)
            progress_callback: Function to report progress
        """
        try:
            logger.info(f"Starting Facebook UID scraping for post: {post_url}")
            start_time = time.time()
            
            # Step 1: Initialize session
            session_result = await self._ensure_facebook_session(profile_id)
            if not session_result["success"]:
                return session_result
            
            # Step 2: Navigate to post
            browser = await self.browser_manager.get_browser(profile_id)
            if not browser:
                return {"success": False, "error": "Browser not available"}
            
            page = await browser.get(post_url)
            await asyncio.sleep(3)
            
            # Step 3: Load all dynamic content
            logger.info("Loading dynamic content...")
            dynamic_result = await self.dynamic_loader.load_all_dynamic_content(
                browser, 
                max_iterations=30,
                progress_callback=self._create_dynamic_progress_callback(progress_callback)
            )
            
            # Step 4: Smart scrolling to load all comments
            logger.info("Starting smart scrolling...")
            scroll_result = await self.smart_scroller.scroll_with_comment_loading(
                browser=browser,
                page_url=post_url,
                uid_extractor=self.uid_extractor
            )
            
            # Step 5: Extract all UIDs
            logger.info("Extracting UIDs...")
            uids = await self._extract_all_uids_from_page(browser)
            
            # Step 6: Deduplicate UIDs
            logger.info("Deduplicating UIDs...")
            new_uids, duplicate_uids = self.deduplication_system.add_uids_batch(list(uids))
            
            # Step 7: Save results
            await self._save_scraping_results(profile_id, post_url, new_uids, {
                "dynamic_loading": dynamic_result,
                "scrolling": scroll_result,
                "total_uids": len(uids),
                "new_uids": len(new_uids),
                "duplicates": len(duplicate_uids)
            })
            
            # Update statistics
            scraping_time = time.time() - start_time
            self.scraping_stats["pages_scraped"] += 1
            self.scraping_stats["total_uids_extracted"] += len(uids)
            self.scraping_stats["unique_uids_found"] += len(new_uids)
            self.scraping_stats["total_scraping_time"] += scraping_time
            
            logger.info(f"Scraping completed: {len(new_uids)} new UIDs found in {scraping_time:.1f}s")
            
            return {
                "success": True,
                "results": {
                    "post_url": post_url,
                    "total_uids_found": len(uids),
                    "new_uids": new_uids,
                    "duplicate_count": len(duplicate_uids),
                    "scraping_time": scraping_time,
                    "dynamic_loading_stats": dynamic_result.get("stats", {}),
                    "scrolling_stats": scroll_result.get("stats", {}),
                    "deduplication_stats": self.deduplication_system.get_statistics()
                }
            }
            
        except Exception as e:
            logger.error(f"Error scraping Facebook post: {e}")
            return {"success": False, "error": str(e)}
    
    async def scrape_multiple_posts(
        self,
        profile_id: str,
        post_urls: List[str],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Scrape multiple Facebook posts in sequence"""
        try:
            results = []
            total_new_uids = []
            
            for i, post_url in enumerate(post_urls):
                logger.info(f"Scraping post {i+1}/{len(post_urls)}: {post_url}")
                
                result = await self.scrape_facebook_post_uids(
                    profile_id=profile_id,
                    post_url=post_url,
                    progress_callback=progress_callback
                )
                
                results.append(result)
                
                if result["success"]:
                    total_new_uids.extend(result["results"]["new_uids"])
                
                # Small delay between posts
                await asyncio.sleep(2)
            
            return {
                "success": True,
                "results": {
                    "posts_scraped": len(post_urls),
                    "successful_scrapes": len([r for r in results if r["success"]]),
                    "total_unique_uids": len(set(total_new_uids)),
                    "individual_results": results,
                    "overall_stats": self.get_scraping_statistics()
                }
            }
            
        except Exception as e:
            logger.error(f"Error scraping multiple posts: {e}")
            return {"success": False, "error": str(e)}
    
    async def _ensure_facebook_session(self, profile_id: str) -> Dict[str, Any]:
        """Ensure Facebook session is active"""
        try:
            # Check if session already exists and is active
            if await self.session_handler.is_session_active(profile_id):
                logger.info(f"Using existing Facebook session for profile: {profile_id}")
                return {"success": True, "message": "Session already active"}
            
            # Initiate new login
            login_result = await self.session_handler.initiate_login(profile_id)
            if not login_result["success"]:
                return login_result
            
            # Wait for manual login completion (in real implementation, this would be handled by UI)
            logger.info("Waiting for manual login completion...")
            max_wait = 120  # 2 minutes
            wait_time = 0
            
            while wait_time < max_wait:
                status = await self.session_handler.check_login_status(profile_id)
                
                if status["success"] and status["status"] == "logged_in":
                    complete_result = await self.session_handler.complete_login(profile_id)
                    self.scraping_stats["sessions_created"] += 1
                    return complete_result
                
                await asyncio.sleep(5)
                wait_time += 5
            
            return {"success": False, "error": "Login timeout - please complete login manually"}
            
        except Exception as e:
            logger.error(f"Error ensuring Facebook session: {e}")
            return {"success": False, "error": str(e)}
    
    async def _extract_all_uids_from_page(self, browser) -> Set[str]:
        """Extract all UIDs from current page"""
        try:
            page = await browser.get_current_page()
            if not page:
                return set()
            
            all_uids = set()
            
            # Extract from page source
            page_uids = await self.uid_extractor.extract_uids_from_page(browser, await page.url)
            all_uids.update(page_uids)
            
            # Extract specifically from comments section
            comment_uids = await self.uid_extractor.extract_uids_from_comments_section(page)
            all_uids.update(comment_uids)
            
            # Filter valid UIDs
            valid_uids = self.uid_extractor.filter_valid_uids(all_uids)
            
            logger.info(f"Extracted {len(valid_uids)} valid UIDs from page")
            return valid_uids
            
        except Exception as e:
            logger.error(f"Error extracting UIDs from page: {e}")
            return set()
    
    async def _save_scraping_results(
        self, 
        profile_id: str, 
        post_url: str, 
        uids: List[str], 
        metadata: Dict[str, Any]
    ):
        """Save scraping results to file"""
        try:
            # Create results file
            timestamp = int(time.time())
            filename = f"scraping_results_{profile_id}_{timestamp}.json"
            results_file = self.persistence_dir / filename
            
            data = {
                "profile_id": profile_id,
                "post_url": post_url,
                "timestamp": timestamp,
                "uids": uids,
                "metadata": metadata,
                "scraping_stats": self.scraping_stats
            }
            
            import json
            with open(results_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            # Save deduplication state
            self.deduplication_system.save_to_file()
            
            logger.info(f"Results saved to {results_file}")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def _create_dynamic_progress_callback(self, main_callback: Optional[Callable]) -> Callable:
        """Create progress callback for dynamic loading"""
        async def callback(stats):
            if main_callback:
                await main_callback({
                    "stage": "dynamic_loading",
                    "stats": stats
                })
        return callback
    
    def get_scraping_statistics(self) -> Dict[str, Any]:
        """Get comprehensive scraping statistics"""
        return {
            "scraping_stats": self.scraping_stats,
            "deduplication_stats": self.deduplication_system.get_statistics(),
            "browser_stats": {
                "active_browsers": len(self.browser_manager.browsers),
                "active_sessions": len(self.session_handler.sessions)
            }
        }
    
    async def cleanup(self):
        """Cleanup all resources"""
        try:
            # Close all browser sessions
            await self.browser_manager.close_all_browsers()
            
            # Save final deduplication state
            self.deduplication_system.save_to_file()
            
            logger.info("Facebook scraper service cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def get_unique_uids_count(self) -> int:
        """Get total count of unique UIDs collected"""
        return len(self.deduplication_system.get_unique_uids())
    
    async def export_all_uids(self, filename: Optional[str] = None) -> str:
        """Export all unique UIDs to file"""
        try:
            if not filename:
                timestamp = int(time.time())
                filename = f"all_unique_uids_{timestamp}.txt"
            
            export_file = self.persistence_dir / filename
            unique_uids = self.deduplication_system.get_unique_uids()
            
            with open(export_file, 'w') as f:
                for uid in sorted(unique_uids):
                    f.write(f"{uid}\n")
            
            logger.info(f"Exported {len(unique_uids)} unique UIDs to {export_file}")
            return str(export_file)
            
        except Exception as e:
            logger.error(f"Error exporting UIDs: {e}")
            return ""
