"""
Smart Scrolling Engine for Facebook with adaptive speed, end detection and human-like patterns
"""

import asyncio
import random
import time
from typing import Dict, Any, Optional, Callable, List
from loguru import logger

from zendriver import Browser


class SmartScrollingEngine:
    """Intelligent scrolling engine with human-like behavior patterns"""
    
    def __init__(self):
        self.scroll_stats = {
            "total_scrolls": 0,
            "total_distance": 0,
            "start_time": None,
            "end_time": None,
            "content_loaded": 0
        }
        
        # Scrolling configuration
        self.config = {
            "min_scroll_delay": 0.5,  # Minimum delay between scrolls
            "max_scroll_delay": 2.0,  # Maximum delay between scrolls
            "scroll_distance_min": 200,  # Minimum scroll distance
            "scroll_distance_max": 800,  # Maximum scroll distance
            "pause_probability": 0.15,  # Probability of random pause
            "pause_duration_min": 1.0,  # Minimum pause duration
            "pause_duration_max": 3.0,  # Maximum pause duration
            "end_detection_attempts": 3,  # Attempts to detect end
            "end_detection_delay": 2.0,  # Delay between end detection attempts
            "max_scroll_time": 300,  # Maximum scrolling time (5 minutes)
            "content_check_interval": 5,  # Check for new content every N scrolls
        }
    
    async def scroll_to_load_all_content(
        self, 
        browser: <PERSON>rows<PERSON>, 
        page_url: str,
        content_detector: Optional[Callable] = None,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Scroll page to load all content with intelligent detection
        
        Args:
            browser: Zendriver browser instance
            page_url: URL to scroll
            content_detector: Function to detect new content
            progress_callback: Function to report progress
        """
        try:
            # Navigate to page
            page = await browser.get(page_url)
            await asyncio.sleep(3)
            
            # Initialize stats
            self.scroll_stats = {
                "total_scrolls": 0,
                "total_distance": 0,
                "start_time": time.time(),
                "end_time": None,
                "content_loaded": 0,
                "page_url": page_url
            }
            
            logger.info(f"Starting intelligent scrolling for: {page_url}")
            
            # Initial content detection
            if content_detector:
                initial_content = await content_detector(page)
                self.scroll_stats["content_loaded"] = initial_content
            
            # Main scrolling loop
            consecutive_no_change = 0
            last_content_count = 0
            last_scroll_height = 0
            
            while True:
                # Check time limit
                if time.time() - self.scroll_stats["start_time"] > self.config["max_scroll_time"]:
                    logger.warning("Maximum scroll time reached")
                    break
                
                # Get current scroll position
                current_height = await self._get_scroll_height(page)
                
                # Perform human-like scroll
                scroll_distance = await self._perform_human_scroll(page)
                self.scroll_stats["total_scrolls"] += 1
                self.scroll_stats["total_distance"] += scroll_distance
                
                # Wait for content to load
                await self._adaptive_wait()
                
                # Check for new content periodically
                if self.scroll_stats["total_scrolls"] % self.config["content_check_interval"] == 0:
                    if content_detector:
                        current_content = await content_detector(page)
                        
                        if current_content > last_content_count:
                            last_content_count = current_content
                            self.scroll_stats["content_loaded"] = current_content
                            consecutive_no_change = 0
                            
                            if progress_callback:
                                await progress_callback({
                                    "scrolls": self.scroll_stats["total_scrolls"],
                                    "content_loaded": current_content,
                                    "time_elapsed": time.time() - self.scroll_stats["start_time"]
                                })
                        else:
                            consecutive_no_change += 1
                
                # Check if we've reached the end
                new_height = await self._get_scroll_height(page)
                if await self._detect_end_of_content(page, current_height, new_height):
                    consecutive_no_change += 1
                    
                    if consecutive_no_change >= self.config["end_detection_attempts"]:
                        logger.info("End of content detected")
                        break
                else:
                    consecutive_no_change = 0
                
                last_scroll_height = new_height
                
                # Random human-like pause
                if random.random() < self.config["pause_probability"]:
                    await self._random_pause()
            
            # Finalize stats
            self.scroll_stats["end_time"] = time.time()
            
            # Final content check
            if content_detector:
                final_content = await content_detector(page)
                self.scroll_stats["content_loaded"] = final_content
            
            logger.info(f"Scrolling completed. Stats: {self.scroll_stats}")
            
            return {
                "success": True,
                "stats": self.scroll_stats,
                "message": "Scrolling completed successfully"
            }
            
        except Exception as e:
            logger.error(f"Error during scrolling: {e}")
            return {
                "success": False,
                "error": str(e),
                "stats": self.scroll_stats
            }
    
    async def _perform_human_scroll(self, page) -> int:
        """Perform human-like scrolling with random variations"""
        try:
            # Random scroll distance
            scroll_distance = random.randint(
                self.config["scroll_distance_min"],
                self.config["scroll_distance_max"]
            )
            
            # Add some randomness to make it more human-like
            variation = random.randint(-50, 50)
            scroll_distance += variation
            
            # Ensure minimum distance
            scroll_distance = max(scroll_distance, 100)
            
            # Perform scroll with smooth animation
            await page.evaluate(f"""
                window.scrollBy({{
                    top: {scroll_distance},
                    left: 0,
                    behavior: 'smooth'
                }});
            """)
            
            return scroll_distance
            
        except Exception as e:
            logger.error(f"Error performing scroll: {e}")
            return 0
    
    async def _adaptive_wait(self):
        """Adaptive waiting with human-like delays"""
        try:
            # Base delay with randomness
            delay = random.uniform(
                self.config["min_scroll_delay"],
                self.config["max_scroll_delay"]
            )
            
            # Add micro-variations for more human-like behavior
            micro_variation = random.uniform(-0.1, 0.1)
            delay += micro_variation
            
            await asyncio.sleep(max(delay, 0.1))
            
        except Exception as e:
            logger.error(f"Error in adaptive wait: {e}")
    
    async def _random_pause(self):
        """Random pause to simulate human reading behavior"""
        try:
            pause_duration = random.uniform(
                self.config["pause_duration_min"],
                self.config["pause_duration_max"]
            )
            
            logger.debug(f"Taking random pause for {pause_duration:.2f} seconds")
            await asyncio.sleep(pause_duration)
            
        except Exception as e:
            logger.error(f"Error in random pause: {e}")
    
    async def _get_scroll_height(self, page) -> int:
        """Get current scroll height of the page"""
        try:
            height = await page.evaluate("document.body.scrollHeight")
            return height or 0
        except Exception as e:
            logger.error(f"Error getting scroll height: {e}")
            return 0
    
    async def _get_scroll_position(self, page) -> int:
        """Get current scroll position"""
        try:
            position = await page.evaluate("window.pageYOffset || document.documentElement.scrollTop")
            return position or 0
        except Exception as e:
            logger.error(f"Error getting scroll position: {e}")
            return 0
    
    async def _detect_end_of_content(self, page, old_height: int, new_height: int) -> bool:
        """Detect if we've reached the end of scrollable content"""
        try:
            # Check if scroll height hasn't changed
            if old_height == new_height:
                return True
            
            # Check if we're at the bottom of the page
            scroll_position = await self._get_scroll_position(page)
            window_height = await page.evaluate("window.innerHeight")
            
            if scroll_position + window_height >= new_height - 100:  # 100px tolerance
                return True
            
            # Check for "end of content" indicators
            end_indicators = await page.evaluate("""
                () => {
                    const indicators = [
                        'No more posts to show',
                        'End of posts',
                        'You\\'ve reached the end',
                        'No more content',
                        'That\\'s all for now'
                    ];
                    
                    const pageText = document.body.innerText.toLowerCase();
                    return indicators.some(indicator => 
                        pageText.includes(indicator.toLowerCase())
                    );
                }
            """)
            
            return bool(end_indicators)
            
        except Exception as e:
            logger.error(f"Error detecting end of content: {e}")
            return False
    
    async def scroll_with_comment_loading(
        self, 
        browser: Browser, 
        page_url: str,
        uid_extractor: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Specialized scrolling for Facebook comments with UID extraction"""
        
        def comment_detector(page):
            """Detect number of comments loaded"""
            return page.evaluate("""
                () => {
                    const commentSelectors = [
                        'div[data-testid="UFI2Comment/root"]',
                        'div[role="article"]',
                        'li[data-testid="comment"]',
                        '.UFIComment'
                    ];
                    
                    let totalComments = 0;
                    commentSelectors.forEach(selector => {
                        totalComments += document.querySelectorAll(selector).length;
                    });
                    
                    return totalComments;
                }
            """)
        
        def progress_reporter(stats):
            """Report scrolling progress"""
            logger.info(f"Scrolling progress: {stats['scrolls']} scrolls, "
                       f"{stats['content_loaded']} comments loaded, "
                       f"{stats['time_elapsed']:.1f}s elapsed")
        
        # Perform scrolling with comment detection
        result = await self.scroll_to_load_all_content(
            browser=browser,
            page_url=page_url,
            content_detector=comment_detector,
            progress_callback=progress_reporter
        )
        
        return result
    
    def get_scroll_statistics(self) -> Dict[str, Any]:
        """Get detailed scrolling statistics"""
        if not self.scroll_stats.get("start_time"):
            return {"error": "No scrolling session found"}
        
        duration = (self.scroll_stats.get("end_time", time.time()) - 
                   self.scroll_stats["start_time"])
        
        return {
            "total_scrolls": self.scroll_stats["total_scrolls"],
            "total_distance": self.scroll_stats["total_distance"],
            "duration_seconds": duration,
            "average_scroll_distance": (
                self.scroll_stats["total_distance"] / max(self.scroll_stats["total_scrolls"], 1)
            ),
            "scrolls_per_minute": (
                self.scroll_stats["total_scrolls"] / max(duration / 60, 1)
            ),
            "content_loaded": self.scroll_stats.get("content_loaded", 0),
            "page_url": self.scroll_stats.get("page_url", "")
        }
