"""
Deduplication System for real-time UID processing and memory-efficient storage
"""

import hashlib
import time
from typing import Set, Dict, List, Any, Optional, Tuple
from collections import defaultdict, deque
from loguru import logger
import json
from pathlib import Path


class UIDDeduplicationSystem:
    """Memory-efficient deduplication system for Facebook UIDs"""
    
    def __init__(self, max_memory_uids: int = 100000, persistence_file: Optional[str] = None):
        # In-memory storage for recent UIDs (most frequently accessed)
        self.memory_uids: Set[str] = set()
        self.max_memory_uids = max_memory_uids
        
        # LRU cache for UID access patterns
        self.uid_access_order = deque(maxlen=max_memory_uids)
        self.uid_access_count = defaultdict(int)
        
        # Bloom filter-like structure for fast negative lookups
        self.bloom_filter = BloomFilter(capacity=1000000, error_rate=0.001)
        
        # Statistics
        self.stats = {
            "total_uids_processed": 0,
            "duplicates_found": 0,
            "unique_uids": 0,
            "memory_hits": 0,
            "bloom_hits": 0,
            "false_positives": 0,
            "start_time": time.time()
        }
        
        # Persistence
        self.persistence_file = persistence_file
        self.persistent_uids: Set[str] = set()
        
        # Load existing data if persistence file exists
        if persistence_file:
            self._load_persistent_data()
    
    def add_uid(self, uid: str) -> bool:
        """
        Add UID to deduplication system
        
        Returns:
            True if UID is new (not duplicate)
            False if UID is duplicate
        """
        try:
            self.stats["total_uids_processed"] += 1
            
            # Quick validation
            if not self._is_valid_uid(uid):
                return False
            
            # Check in-memory cache first (fastest)
            if uid in self.memory_uids:
                self.stats["duplicates_found"] += 1
                self.stats["memory_hits"] += 1
                self._update_access_pattern(uid)
                return False
            
            # Check bloom filter (fast negative lookup)
            if self.bloom_filter.might_contain(uid):
                self.stats["bloom_hits"] += 1
                
                # Check persistent storage (slower but definitive)
                if uid in self.persistent_uids:
                    self.stats["duplicates_found"] += 1
                    # Move to memory cache for faster future access
                    self._add_to_memory_cache(uid)
                    return False
                else:
                    # False positive in bloom filter
                    self.stats["false_positives"] += 1
            
            # UID is new - add to all storage layers
            self._add_new_uid(uid)
            self.stats["unique_uids"] += 1
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding UID {uid}: {e}")
            return False
    
    def add_uids_batch(self, uids: List[str]) -> Tuple[List[str], List[str]]:
        """
        Add multiple UIDs in batch
        
        Returns:
            Tuple of (new_uids, duplicate_uids)
        """
        new_uids = []
        duplicate_uids = []
        
        try:
            for uid in uids:
                if self.add_uid(uid):
                    new_uids.append(uid)
                else:
                    duplicate_uids.append(uid)
            
            logger.info(f"Batch processed: {len(new_uids)} new, {len(duplicate_uids)} duplicates")
            
            return new_uids, duplicate_uids
            
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            return new_uids, duplicate_uids
    
    def _add_new_uid(self, uid: str):
        """Add new UID to all storage layers"""
        # Add to bloom filter
        self.bloom_filter.add(uid)
        
        # Add to memory cache
        self._add_to_memory_cache(uid)
        
        # Add to persistent storage
        if self.persistence_file:
            self.persistent_uids.add(uid)
    
    def _add_to_memory_cache(self, uid: str):
        """Add UID to memory cache with LRU eviction"""
        # If memory is full, evict least recently used
        if len(self.memory_uids) >= self.max_memory_uids:
            self._evict_lru_uid()
        
        self.memory_uids.add(uid)
        self._update_access_pattern(uid)
    
    def _update_access_pattern(self, uid: str):
        """Update access pattern for LRU cache"""
        # Remove from current position if exists
        if uid in self.uid_access_order:
            temp_deque = deque()
            while self.uid_access_order:
                item = self.uid_access_order.popleft()
                if item != uid:
                    temp_deque.append(item)
            self.uid_access_order = temp_deque
        
        # Add to end (most recent)
        self.uid_access_order.append(uid)
        self.uid_access_count[uid] += 1
    
    def _evict_lru_uid(self):
        """Evict least recently used UID from memory"""
        if self.uid_access_order:
            lru_uid = self.uid_access_order.popleft()
            self.memory_uids.discard(lru_uid)
            
            # Clean up access count if very low
            if self.uid_access_count[lru_uid] <= 1:
                del self.uid_access_count[lru_uid]
    
    def _is_valid_uid(self, uid: str) -> bool:
        """Validate UID format"""
        return (
            uid and 
            uid.isdigit() and 
            8 <= len(uid) <= 20 and
            uid != '0' * len(uid)
        )
    
    def contains(self, uid: str) -> bool:
        """Check if UID exists in system"""
        if uid in self.memory_uids:
            return True
        
        if self.bloom_filter.might_contain(uid):
            return uid in self.persistent_uids
        
        return False
    
    def get_unique_uids(self) -> Set[str]:
        """Get all unique UIDs (memory + persistent)"""
        return self.memory_uids.union(self.persistent_uids)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get deduplication statistics"""
        runtime = time.time() - self.stats["start_time"]
        
        return {
            **self.stats,
            "runtime_seconds": runtime,
            "memory_uids_count": len(self.memory_uids),
            "persistent_uids_count": len(self.persistent_uids),
            "total_unique_uids": len(self.get_unique_uids()),
            "deduplication_rate": (
                self.stats["duplicates_found"] / max(self.stats["total_uids_processed"], 1)
            ),
            "memory_hit_rate": (
                self.stats["memory_hits"] / max(self.stats["total_uids_processed"], 1)
            ),
            "bloom_accuracy": (
                1 - (self.stats["false_positives"] / max(self.stats["bloom_hits"], 1))
            ),
            "processing_rate": (
                self.stats["total_uids_processed"] / max(runtime, 1)
            )
        }
    
    def save_to_file(self, filename: Optional[str] = None):
        """Save current state to file"""
        try:
            save_file = filename or self.persistence_file
            if not save_file:
                logger.warning("No persistence file specified")
                return False
            
            data = {
                "memory_uids": list(self.memory_uids),
                "persistent_uids": list(self.persistent_uids),
                "stats": self.stats,
                "timestamp": time.time()
            }
            
            with open(save_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Deduplication data saved to {save_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving to file: {e}")
            return False
    
    def _load_persistent_data(self):
        """Load persistent data from file"""
        try:
            if not Path(self.persistence_file).exists():
                logger.info("No existing persistence file found")
                return
            
            with open(self.persistence_file, 'r') as f:
                data = json.load(f)
            
            # Load UIDs
            self.persistent_uids = set(data.get("persistent_uids", []))
            memory_uids = data.get("memory_uids", [])
            
            # Rebuild bloom filter
            all_uids = self.persistent_uids.union(set(memory_uids))
            for uid in all_uids:
                self.bloom_filter.add(uid)
            
            # Load recent UIDs to memory
            for uid in memory_uids[-self.max_memory_uids:]:
                self.memory_uids.add(uid)
                self.uid_access_order.append(uid)
            
            logger.info(f"Loaded {len(all_uids)} UIDs from persistence file")
            
        except Exception as e:
            logger.error(f"Error loading persistent data: {e}")
    
    def clear_memory_cache(self):
        """Clear memory cache (keep persistent storage)"""
        self.memory_uids.clear()
        self.uid_access_order.clear()
        self.uid_access_count.clear()
        logger.info("Memory cache cleared")
    
    def optimize_memory(self):
        """Optimize memory usage by cleaning up old data"""
        # Move least accessed UIDs from memory to persistent storage
        if len(self.memory_uids) > self.max_memory_uids * 0.8:
            # Sort by access count
            sorted_uids = sorted(
                self.uid_access_count.items(),
                key=lambda x: x[1]
            )
            
            # Move bottom 20% to persistent storage
            uids_to_move = int(len(sorted_uids) * 0.2)
            for uid, _ in sorted_uids[:uids_to_move]:
                if uid in self.memory_uids:
                    self.memory_uids.remove(uid)
                    self.persistent_uids.add(uid)
                    del self.uid_access_count[uid]
            
            logger.info(f"Moved {uids_to_move} UIDs from memory to persistent storage")


class BloomFilter:
    """Simple Bloom Filter implementation for fast negative lookups"""
    
    def __init__(self, capacity: int, error_rate: float):
        self.capacity = capacity
        self.error_rate = error_rate
        
        # Calculate optimal parameters
        self.bit_array_size = self._calculate_bit_array_size()
        self.hash_count = self._calculate_hash_count()
        
        # Initialize bit array
        self.bit_array = [False] * self.bit_array_size
        self.items_count = 0
    
    def _calculate_bit_array_size(self) -> int:
        """Calculate optimal bit array size"""
        import math
        return int(-self.capacity * math.log(self.error_rate) / (math.log(2) ** 2))
    
    def _calculate_hash_count(self) -> int:
        """Calculate optimal number of hash functions"""
        import math
        return int(self.bit_array_size * math.log(2) / self.capacity)
    
    def _hash(self, item: str, seed: int) -> int:
        """Generate hash for item with seed"""
        hash_obj = hashlib.md5(f"{item}{seed}".encode())
        return int(hash_obj.hexdigest(), 16) % self.bit_array_size
    
    def add(self, item: str):
        """Add item to bloom filter"""
        for i in range(self.hash_count):
            index = self._hash(item, i)
            self.bit_array[index] = True
        self.items_count += 1
    
    def might_contain(self, item: str) -> bool:
        """Check if item might be in the set"""
        for i in range(self.hash_count):
            index = self._hash(item, i)
            if not self.bit_array[index]:
                return False
        return True
