"""
Comprehensive test suite for FacebookScraperService
Tests all components including parallel processing, error handling, memory optimization, and performance monitoring
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

# Import the service and components
from automation.facebook_scraper_service import FacebookScraperService
from automation.parallel_processor import ParallelProcessor, ProcessingTask, ProcessingResult
from automation.error_handler import RobustErrorHandler, RetryConfig, ErrorType
from automation.memory_optimizer import MemoryOptimizer
from automation.performance_monitor import PerformanceMonitor
from automation.deduplication_system import UIDDeduplicationSystem


class TestFacebookScraperService:
    """Test suite for FacebookScraperService"""
    
    @pytest.fixture
    async def scraper_service(self):
        """Create a FacebookScraperService instance for testing"""
        service = FacebookScraperService()
        yield service
        await service.cleanup()
    
    @pytest.fixture
    def mock_browser_manager(self):
        """Mock browser manager"""
        mock_manager = AsyncMock()
        mock_manager.get_browser.return_value = AsyncMock()
        mock_manager.close_browser.return_value = None
        return mock_manager
    
    @pytest.fixture
    def sample_post_urls(self):
        """Sample Facebook post URLs for testing"""
        return [
            "https://www.facebook.com/groups/123456789/posts/987654321/",
            "https://www.facebook.com/groups/123456789/posts/987654322/",
            "https://www.facebook.com/groups/123456789/posts/987654323/"
        ]
    
    @pytest.fixture
    def sample_uids(self):
        """Sample UIDs for testing"""
        return ["1234567890", "2345678901", "3456789012", "4567890123"]
    
    async def test_service_initialization(self, scraper_service):
        """Test service initialization"""
        assert scraper_service is not None
        assert hasattr(scraper_service, 'parallel_processor')
        assert hasattr(scraper_service, 'error_handler')
        assert hasattr(scraper_service, 'memory_optimizer')
        assert hasattr(scraper_service, 'performance_monitor')
        assert hasattr(scraper_service, 'deduplication_system')
    
    async def test_single_post_scraping(self, scraper_service, sample_post_urls, sample_uids):
        """Test single post scraping functionality"""
        with patch.object(scraper_service, '_scrape_facebook_post_uids_internal') as mock_scrape:
            mock_scrape.return_value = {
                "success": True,
                "results": {
                    "uids_found": sample_uids,
                    "total_uids_found": len(sample_uids),
                    "new_uids": sample_uids,
                    "duplicate_uids": [],
                    "scraping_time": 30.5
                }
            }
            
            result = await scraper_service.scrape_facebook_post_uids(
                profile_id="test_profile",
                post_url=sample_post_urls[0]
            )
            
            assert result["success"] is True
            assert result["results"]["total_uids_found"] == len(sample_uids)
            assert len(result["results"]["uids_found"]) == len(sample_uids)
            mock_scrape.assert_called_once()
    
    async def test_multiple_posts_scraping(self, scraper_service, sample_post_urls, sample_uids):
        """Test multiple posts scraping"""
        with patch.object(scraper_service, '_scrape_facebook_post_uids_internal') as mock_scrape:
            mock_scrape.return_value = {
                "success": True,
                "results": {
                    "uids_found": sample_uids[:2],  # Different UIDs per post
                    "total_uids_found": 2,
                    "new_uids": sample_uids[:2],
                    "duplicate_uids": [],
                    "scraping_time": 25.0
                }
            }
            
            result = await scraper_service.scrape_multiple_posts(
                profile_id="test_profile",
                post_urls=sample_post_urls[:2]
            )
            
            assert result["success"] is True
            assert "all_unique_uids" in result["results"]
            assert result["results"]["total_posts_processed"] == 2
    
    async def test_parallel_processing(self, scraper_service, sample_post_urls):
        """Test parallel processing functionality"""
        with patch.object(scraper_service.parallel_processor, 'process_posts_parallel') as mock_parallel:
            mock_parallel.return_value = {
                "success": True,
                "results": {
                    "all_unique_uids": ["1111", "2222", "3333"],
                    "total_posts_processed": 3,
                    "processing_time": 45.0,
                    "performance_metrics": {
                        "avg_processing_time": 15.0,
                        "max_concurrent_workers": 3
                    }
                }
            }
            
            result = await scraper_service.scrape_multiple_posts_parallel(
                profile_id="test_profile",
                post_urls=sample_post_urls,
                max_concurrent=3
            )
            
            assert result["success"] is True
            assert len(result["results"]["all_unique_uids"]) == 3
            mock_parallel.assert_called_once()
    
    async def test_memory_optimization(self, scraper_service, sample_post_urls):
        """Test memory optimization functionality"""
        with patch.object(scraper_service, '_stream_process_posts') as mock_stream:
            mock_stream.return_value = {
                "success": True,
                "results": {
                    "all_unique_uids": ["4444", "5555"],
                    "total_posts_processed": 2,
                    "memory_stats": {
                        "peak_memory_mb": 150.5,
                        "memory_optimized": True
                    }
                }
            }
            
            result = await scraper_service.scrape_with_memory_optimization(
                profile_id="test_profile",
                post_urls=sample_post_urls[:2]
            )
            
            assert result["success"] is True
            assert result["results"]["memory_stats"]["memory_optimized"] is True
            mock_stream.assert_called_once()
    
    async def test_auto_optimization(self, scraper_service, sample_post_urls):
        """Test auto optimization mode"""
        with patch.object(scraper_service, 'scrape_multiple_posts_parallel') as mock_parallel:
            mock_parallel.return_value = {
                "success": True,
                "results": {"optimization_mode": "parallel"}
            }
            
            result = await scraper_service.scrape_with_auto_optimization(
                profile_id="test_profile",
                post_urls=sample_post_urls
            )
            
            assert result["success"] is True
            mock_parallel.assert_called_once()
    
    async def test_error_handling(self, scraper_service, sample_post_urls):
        """Test error handling and retry logic"""
        with patch.object(scraper_service, '_scrape_facebook_post_uids_internal') as mock_scrape:
            # First call fails, second succeeds
            mock_scrape.side_effect = [
                Exception("Network error"),
                {
                    "success": True,
                    "results": {"uids_found": ["retry_uid"], "total_uids_found": 1}
                }
            ]
            
            with patch.object(scraper_service.error_handler, 'handle_with_retry') as mock_retry:
                mock_retry.return_value = {
                    "success": True,
                    "results": {"uids_found": ["retry_uid"], "total_uids_found": 1}
                }
                
                result = await scraper_service.scrape_facebook_post_uids(
                    profile_id="test_profile",
                    post_url=sample_post_urls[0]
                )
                
                assert result["success"] is True
    
    async def test_deduplication(self, scraper_service):
        """Test UID deduplication"""
        test_uids = ["1111", "2222", "1111", "3333", "2222"]  # Contains duplicates
        
        with patch.object(scraper_service.deduplication_system, 'process_uids') as mock_dedup:
            mock_dedup.return_value = {
                "new_uids": ["1111", "2222", "3333"],
                "duplicate_uids": ["1111", "2222"],
                "total_processed": 5
            }
            
            result = scraper_service.deduplication_system.process_uids(test_uids)
            
            assert len(result["new_uids"]) == 3
            assert len(result["duplicate_uids"]) == 2
            mock_dedup.assert_called_once()
    
    async def test_performance_monitoring(self, scraper_service):
        """Test performance monitoring"""
        # Test performance report generation
        with patch.object(scraper_service.performance_monitor, 'get_comprehensive_report') as mock_report:
            mock_report.return_value = {
                "timestamp": time.time(),
                "system_metrics": {"cpu_percent": 45.0, "memory_percent": 60.0},
                "operation_summary": {"scrape_facebook_post_uids": {"total_executions": 5}},
                "monitoring_active": True
            }
            
            report = scraper_service.get_performance_report()
            
            assert "system_metrics" in report
            assert "operation_summary" in report
            assert report["monitoring_active"] is True
            mock_report.assert_called_once()
    
    async def test_statistics_collection(self, scraper_service):
        """Test statistics collection"""
        stats = scraper_service.get_scraping_statistics()
        
        assert "total_posts_scraped" in stats
        assert "total_uids_found" in stats
        assert "average_scraping_time" in stats
        assert "error_stats" in stats
        assert "parallel_stats" in stats
        assert "memory_stats" in stats
    
    async def test_cleanup(self, scraper_service):
        """Test service cleanup"""
        with patch.object(scraper_service.browser_manager, 'close_all_browsers') as mock_close:
            with patch.object(scraper_service.memory_optimizer, 'cleanup') as mock_memory_cleanup:
                with patch.object(scraper_service.performance_monitor, 'cleanup') as mock_perf_cleanup:
                    await scraper_service.cleanup()
                    
                    mock_close.assert_called_once()
                    mock_memory_cleanup.assert_called_once()
                    mock_perf_cleanup.assert_called_once()


class TestParallelProcessor:
    """Test suite for ParallelProcessor"""
    
    @pytest.fixture
    def mock_scraper_service(self):
        """Mock scraper service"""
        return AsyncMock()
    
    @pytest.fixture
    def parallel_processor(self, mock_scraper_service):
        """Create ParallelProcessor instance"""
        return ParallelProcessor(mock_scraper_service)
    
    async def test_resource_monitoring(self, parallel_processor):
        """Test resource monitoring"""
        monitor = parallel_processor.resource_monitor
        
        assert monitor.max_browsers > 0
        assert monitor.max_memory_mb > 0
        
        # Test resource check
        can_process = monitor.can_process_more()
        assert isinstance(can_process, bool)
    
    async def test_task_processing(self, parallel_processor):
        """Test task processing"""
        tasks = [
            ProcessingTask(
                task_id="task_1",
                profile_id="profile_1",
                post_url="https://facebook.com/post/1",
                priority=1
            ),
            ProcessingTask(
                task_id="task_2",
                profile_id="profile_1",
                post_url="https://facebook.com/post/2",
                priority=2
            )
        ]
        
        with patch.object(parallel_processor.scraper_service, 'scrape_facebook_post_uids') as mock_scrape:
            mock_scrape.return_value = {
                "success": True,
                "results": {"uids_found": ["test_uid"], "total_uids_found": 1}
            }
            
            results = await parallel_processor.process_tasks_parallel(tasks, max_concurrent=2)
            
            assert len(results) == 2
            assert all(isinstance(r, ProcessingResult) for r in results)


class TestErrorHandler:
    """Test suite for RobustErrorHandler"""
    
    @pytest.fixture
    def error_handler(self):
        """Create error handler instance"""
        config = RetryConfig(max_attempts=3, base_delay=0.1)
        return RobustErrorHandler(config)
    
    async def test_error_classification(self, error_handler):
        """Test error classification"""
        network_error = Exception("Connection timeout")
        browser_error = Exception("Browser crashed")
        
        network_type = error_handler.error_classifier.classify_error(network_error)
        browser_type = error_handler.error_classifier.classify_error(browser_error)
        
        assert network_type in [ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR, ErrorType.UNKNOWN_ERROR]
        assert browser_type in [ErrorType.BROWSER_ERROR, ErrorType.UNKNOWN_ERROR]
    
    async def test_retry_logic(self, error_handler):
        """Test retry logic"""
        call_count = 0
        
        async def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return {"success": True, "result": "success"}
        
        result = await error_handler.handle_with_retry(
            failing_function,
            operation_name="test_operation"
        )
        
        assert result["success"] is True
        assert call_count == 3


class TestMemoryOptimizer:
    """Test suite for MemoryOptimizer"""
    
    @pytest.fixture
    def memory_optimizer(self):
        """Create memory optimizer instance"""
        return MemoryOptimizer()
    
    async def test_memory_monitoring(self, memory_optimizer):
        """Test memory monitoring"""
        monitor = memory_optimizer.memory_monitor
        
        # Test memory statistics
        stats = monitor.get_memory_statistics()
        assert "current_memory_mb" in stats
        assert "memory_percent" in stats
    
    async def test_streaming_processing(self, memory_optimizer):
        """Test streaming processing"""
        test_data = [f"item_{i}" for i in range(100)]
        
        async def process_chunk(chunk):
            return [f"processed_{item}" for item in chunk]
        
        results = []
        async for processed_chunk in memory_optimizer.streaming_processor.stream_process(
            test_data, process_chunk, chunk_size=10
        ):
            results.extend(processed_chunk)
        
        assert len(results) == 100
        assert all("processed_" in item for item in results)


class TestPerformanceMonitor:
    """Test suite for PerformanceMonitor"""
    
    @pytest.fixture
    def performance_monitor(self):
        """Create performance monitor instance"""
        return PerformanceMonitor(collection_interval=1.0)
    
    async def test_metrics_collection(self, performance_monitor):
        """Test metrics collection"""
        # Record some test metrics
        performance_monitor.record_custom_metric("test_metric", 42.0)
        performance_monitor.increment_counter("test_counter", 5)
        performance_monitor.set_gauge("test_gauge", 100.0)
        
        # Get metrics summary
        summary = performance_monitor.metrics_collector.get_metrics_summary(time_window=60)
        
        assert len(summary) > 0
    
    async def test_operation_profiling(self, performance_monitor):
        """Test operation profiling"""
        async with performance_monitor.profile_operation("test_operation"):
            await asyncio.sleep(0.1)  # Simulate work
        
        # Check operation was recorded
        summary = performance_monitor.metrics_collector.get_operation_summary("test_operation")
        assert summary["total_executions"] == 1
        assert summary["avg_duration_ms"] > 0
    
    async def test_anomaly_detection(self, performance_monitor):
        """Test anomaly detection"""
        # Establish baseline
        for i in range(20):
            performance_monitor.record_custom_metric("timer.test_operation", 0.1)
        
        performance_monitor.analyzer.establish_baseline()
        
        # Record anomalous value
        performance_monitor.record_custom_metric("timer.test_operation", 1.0)  # 10x normal
        
        anomalies = performance_monitor.analyzer.detect_anomalies()
        # Note: May or may not detect anomaly depending on baseline establishment
        assert isinstance(anomalies, list)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
