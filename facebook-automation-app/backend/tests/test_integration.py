"""
Integration tests for Facebook Scraper Service
Tests end-to-end functionality and component integration
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List
import tempfile
import os

# Import the service and API
from automation.facebook_scraper_service import FacebookScraperService
from api.scraping import router
from fastapi.testclient import TestClient
from fastapi import FastAPI


class TestIntegration:
    """Integration test suite"""
    
    @pytest.fixture
    async def scraper_service(self):
        """Create a real FacebookScraperService instance"""
        service = FacebookScraperService()
        yield service
        await service.cleanup()
    
    @pytest.fixture
    def test_app(self):
        """Create test FastAPI app"""
        app = FastAPI()
        app.include_router(router)
        return app
    
    @pytest.fixture
    def client(self, test_app):
        """Create test client"""
        return TestClient(test_app)
    
    @pytest.fixture
    def sample_facebook_urls(self):
        """Sample Facebook URLs for testing"""
        return [
            "https://www.facebook.com/groups/591054007361950/posts/1234567890/",
            "https://www.facebook.com/groups/591054007361950/posts/1234567891/",
            "https://www.facebook.com/groups/591054007361950/posts/1234567892/"
        ]
    
    async def test_end_to_end_scraping_workflow(self, scraper_service, sample_facebook_urls):
        """Test complete scraping workflow from start to finish"""
        
        # Mock browser operations
        with patch.object(scraper_service.browser_manager, 'get_browser') as mock_get_browser:
            mock_browser = AsyncMock()
            mock_page = AsyncMock()
            
            # Mock browser and page
            mock_browser.new_page.return_value = mock_page
            mock_get_browser.return_value = mock_browser
            
            # Mock page operations
            mock_page.goto.return_value = None
            mock_page.wait_for_load_state.return_value = None
            mock_page.evaluate.return_value = ["1234567890", "2345678901", "3456789012"]
            mock_page.locator.return_value.count.return_value = 10
            
            # Test single post scraping
            result = await scraper_service.scrape_facebook_post_uids(
                profile_id="test_profile",
                post_url=sample_facebook_urls[0],
                max_scroll_time=30
            )
            
            assert result["success"] is True
            assert "results" in result
            assert "uids_found" in result["results"]
            
            # Verify browser was called
            mock_get_browser.assert_called()
            mock_browser.new_page.assert_called()
    
    async def test_parallel_processing_integration(self, scraper_service, sample_facebook_urls):
        """Test parallel processing with multiple posts"""
        
        with patch.object(scraper_service, '_scrape_facebook_post_uids_internal') as mock_scrape:
            # Mock different results for each post
            mock_scrape.side_effect = [
                {
                    "success": True,
                    "results": {
                        "uids_found": ["1111", "2222"],
                        "total_uids_found": 2,
                        "new_uids": ["1111", "2222"],
                        "duplicate_uids": [],
                        "scraping_time": 25.0
                    }
                },
                {
                    "success": True,
                    "results": {
                        "uids_found": ["3333", "4444"],
                        "total_uids_found": 2,
                        "new_uids": ["3333", "4444"],
                        "duplicate_uids": [],
                        "scraping_time": 30.0
                    }
                },
                {
                    "success": True,
                    "results": {
                        "uids_found": ["5555", "1111"],  # 1111 is duplicate
                        "total_uids_found": 2,
                        "new_uids": ["5555"],
                        "duplicate_uids": ["1111"],
                        "scraping_time": 28.0
                    }
                }
            ]
            
            result = await scraper_service.scrape_multiple_posts_parallel(
                profile_id="test_profile",
                post_urls=sample_facebook_urls,
                max_concurrent=2
            )
            
            assert result["success"] is True
            assert "all_unique_uids" in result["results"]
            assert len(result["results"]["all_unique_uids"]) == 5  # 1111, 2222, 3333, 4444, 5555
            assert result["results"]["total_posts_processed"] == 3
    
    async def test_memory_optimization_integration(self, scraper_service, sample_facebook_urls):
        """Test memory optimization with streaming processing"""
        
        with patch.object(scraper_service, '_scrape_facebook_post_uids_internal') as mock_scrape:
            mock_scrape.return_value = {
                "success": True,
                "results": {
                    "uids_found": ["mem_uid_1", "mem_uid_2"],
                    "total_uids_found": 2,
                    "new_uids": ["mem_uid_1", "mem_uid_2"],
                    "duplicate_uids": [],
                    "scraping_time": 20.0
                }
            }
            
            result = await scraper_service.scrape_with_memory_optimization(
                profile_id="test_profile",
                post_urls=sample_facebook_urls[:2]
            )
            
            assert result["success"] is True
            assert "memory_stats" in result["results"]
            assert result["results"]["memory_stats"]["memory_optimized"] is True
    
    async def test_error_recovery_integration(self, scraper_service, sample_facebook_urls):
        """Test error handling and recovery across components"""
        
        with patch.object(scraper_service, '_scrape_facebook_post_uids_internal') as mock_scrape:
            # First call fails, second succeeds
            mock_scrape.side_effect = [
                Exception("Network timeout"),
                {
                    "success": True,
                    "results": {
                        "uids_found": ["recovered_uid"],
                        "total_uids_found": 1,
                        "new_uids": ["recovered_uid"],
                        "duplicate_uids": [],
                        "scraping_time": 35.0
                    }
                }
            ]
            
            # Mock the error handler to actually retry
            with patch.object(scraper_service.error_handler, 'handle_with_retry') as mock_retry:
                mock_retry.return_value = {
                    "success": True,
                    "results": {
                        "uids_found": ["recovered_uid"],
                        "total_uids_found": 1,
                        "new_uids": ["recovered_uid"],
                        "duplicate_uids": [],
                        "scraping_time": 35.0
                    }
                }
                
                result = await scraper_service.scrape_facebook_post_uids(
                    profile_id="test_profile",
                    post_url=sample_facebook_urls[0]
                )
                
                assert result["success"] is True
                assert "recovered_uid" in result["results"]["uids_found"]
    
    async def test_performance_monitoring_integration(self, scraper_service):
        """Test performance monitoring during actual operations"""
        
        # Start performance monitoring
        await scraper_service.performance_monitor.start_monitoring()
        
        # Simulate some operations
        scraper_service.performance_monitor.record_custom_metric("test_operation", 42.0)
        scraper_service.performance_monitor.increment_counter("test_counter")
        
        # Wait a bit for monitoring to collect data
        await asyncio.sleep(1.1)
        
        # Get performance report
        report = scraper_service.get_performance_report()
        
        assert "system_metrics" in report
        assert "operation_summary" in report
        assert "monitoring_active" in report
        assert report["monitoring_active"] is True
        
        # Stop monitoring
        await scraper_service.performance_monitor.stop_monitoring()
    
    async def test_deduplication_across_sessions(self, scraper_service):
        """Test UID deduplication across multiple scraping sessions"""
        
        # First session
        uids_session_1 = ["uid_1", "uid_2", "uid_3"]
        result_1 = scraper_service.deduplication_system.process_uids(uids_session_1)
        
        assert len(result_1["new_uids"]) == 3
        assert len(result_1["duplicate_uids"]) == 0
        
        # Second session with some duplicates
        uids_session_2 = ["uid_2", "uid_3", "uid_4", "uid_5"]  # uid_2 and uid_3 are duplicates
        result_2 = scraper_service.deduplication_system.process_uids(uids_session_2)
        
        assert len(result_2["new_uids"]) == 2  # uid_4 and uid_5
        assert len(result_2["duplicate_uids"]) == 2  # uid_2 and uid_3
    
    async def test_statistics_aggregation(self, scraper_service):
        """Test statistics aggregation across all components"""
        
        # Simulate some scraping activity
        with patch.object(scraper_service, '_scrape_facebook_post_uids_internal') as mock_scrape:
            mock_scrape.return_value = {
                "success": True,
                "results": {
                    "uids_found": ["stat_uid_1", "stat_uid_2"],
                    "total_uids_found": 2,
                    "new_uids": ["stat_uid_1", "stat_uid_2"],
                    "duplicate_uids": [],
                    "scraping_time": 25.0
                }
            }
            
            # Perform multiple scraping operations
            for i in range(3):
                await scraper_service.scrape_facebook_post_uids(
                    profile_id="test_profile",
                    post_url=f"https://facebook.com/post/{i}"
                )
        
        # Get comprehensive statistics
        stats = scraper_service.get_scraping_statistics()
        
        assert "total_posts_scraped" in stats
        assert "total_uids_found" in stats
        assert "average_scraping_time" in stats
        assert "error_stats" in stats
        assert "parallel_stats" in stats
        assert "memory_stats" in stats
        assert "performance_report" in stats
    
    def test_api_integration_advanced_scraping(self, client):
        """Test API integration for advanced scraping endpoints"""
        
        # Mock the facebook_scraper_service
        with patch('api.scraping.facebook_scraper_service') as mock_service:
            mock_service.scrape_with_auto_optimization.return_value = {
                "success": True,
                "results": {
                    "all_unique_uids": ["api_uid_1", "api_uid_2"],
                    "total_posts_processed": 2,
                    "optimization_mode": "auto"
                }
            }
            
            # Test advanced scraping endpoint
            response = client.post("/api/scraping/advanced-scraping", json={
                "profile_id": "test_profile",
                "post_urls": [
                    "https://facebook.com/post/1",
                    "https://facebook.com/post/2"
                ],
                "optimization_mode": "auto",
                "enable_performance_monitoring": True,
                "session_name": "Test Advanced Scraping"
            })
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "session_id" in data
            assert data["optimization_mode"] == "auto"
    
    def test_api_integration_performance_report(self, client):
        """Test API integration for performance reporting"""
        
        with patch('api.scraping.facebook_scraper_service') as mock_service:
            mock_service.get_performance_report.return_value = {
                "timestamp": time.time(),
                "system_metrics": {"cpu_percent": 45.0, "memory_percent": 60.0},
                "operation_summary": {"test_operation": {"total_executions": 5}},
                "metrics_summary": {},
                "performance_trends": {},
                "recent_anomalies": [],
                "baseline_established": True,
                "monitoring_active": True
            }
            
            response = client.get("/api/scraping/performance-report")
            
            assert response.status_code == 200
            data = response.json()
            assert "system_metrics" in data
            assert "operation_summary" in data
            assert data["monitoring_active"] is True
    
    def test_api_integration_parallel_scraping(self, client):
        """Test API integration for parallel scraping"""
        
        with patch('api.scraping.facebook_scraper_service') as mock_service:
            mock_service.scrape_multiple_posts_parallel.return_value = {
                "success": True,
                "results": {
                    "all_unique_uids": ["parallel_uid_1", "parallel_uid_2", "parallel_uid_3"],
                    "total_posts_processed": 3,
                    "performance_metrics": {
                        "max_concurrent_workers": 3,
                        "avg_processing_time": 20.0
                    }
                }
            }
            
            response = client.post("/api/scraping/parallel-scraping", json={
                "profile_id": "test_profile",
                "post_urls": [
                    "https://facebook.com/post/1",
                    "https://facebook.com/post/2",
                    "https://facebook.com/post/3"
                ],
                "max_concurrent": 3,
                "session_name": "Test Parallel Scraping"
            })
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["max_concurrent"] == 3
    
    def test_api_integration_memory_optimized_scraping(self, client):
        """Test API integration for memory-optimized scraping"""
        
        with patch('api.scraping.facebook_scraper_service') as mock_service:
            mock_service.scrape_with_memory_optimization.return_value = {
                "success": True,
                "results": {
                    "all_unique_uids": ["memory_uid_1", "memory_uid_2"],
                    "total_posts_processed": 2,
                    "memory_stats": {
                        "peak_memory_mb": 120.5,
                        "memory_optimized": True,
                        "streaming_enabled": True
                    }
                }
            }
            
            response = client.post("/api/scraping/memory-optimized-scraping", json={
                "profile_id": "test_profile",
                "post_urls": [
                    "https://facebook.com/post/1",
                    "https://facebook.com/post/2"
                ],
                "session_name": "Test Memory Optimized Scraping"
            })
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["streaming_enabled"] is True
    
    async def test_cleanup_integration(self, scraper_service):
        """Test comprehensive cleanup across all components"""
        
        # Start monitoring
        await scraper_service.performance_monitor.start_monitoring()
        
        # Simulate some activity
        scraper_service.performance_monitor.record_custom_metric("cleanup_test", 1.0)
        
        # Perform cleanup
        await scraper_service.cleanup()
        
        # Verify cleanup was performed
        assert not scraper_service.performance_monitor.monitoring_active


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
