"""
Pytest configuration and fixtures for Facebook Scraper Service tests
"""

import pytest
import asyncio
import tempfile
import os

# Configure pytest-asyncio
pytest_plugins = ('pytest_asyncio',)
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Import test dependencies
import pytest_asyncio


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def mock_zendriver():
    """Mock zendriver browser for testing"""
    with patch('automation.browser_manager.zendriver') as mock_zd:
        mock_browser = AsyncMock()
        mock_page = AsyncMock()
        
        # Setup mock browser
        mock_browser.new_page.return_value = mock_page
        mock_browser.close.return_value = None
        
        # Setup mock page
        mock_page.goto.return_value = None
        mock_page.wait_for_load_state.return_value = None
        mock_page.evaluate.return_value = []
        mock_page.locator.return_value.count.return_value = 0
        mock_page.close.return_value = None
        
        # Setup mock zendriver
        mock_zd.start.return_value = mock_browser
        
        yield mock_zd


@pytest.fixture
def mock_database():
    """Mock database session for testing"""
    mock_db = AsyncMock()
    mock_db.add.return_value = None
    mock_db.commit.return_value = None
    mock_db.refresh.return_value = None
    mock_db.get.return_value = None
    return mock_db


@pytest.fixture
def sample_facebook_post_html():
    """Sample Facebook post HTML for testing"""
    return """
    <div class="comment">
        <a href="/groups/123456789/user/1234567890">User 1</a>
        <span>This is a comment</span>
    </div>
    <div class="comment">
        <a href="/groups/123456789/user/2345678901">User 2</a>
        <span>Another comment</span>
    </div>
    <div class="comment">
        <a href="https://www.facebook.com/profile.php?id=3456789012">User 3</a>
        <span>Third comment</span>
    </div>
    """


@pytest.fixture
def sample_uids():
    """Sample UIDs for testing"""
    return [
        "1234567890",
        "2345678901", 
        "3456789012",
        "4567890123",
        "5678901234"
    ]


@pytest.fixture
def sample_post_urls():
    """Sample Facebook post URLs"""
    return [
        "https://www.facebook.com/groups/591054007361950/posts/1234567890/",
        "https://www.facebook.com/groups/591054007361950/posts/1234567891/",
        "https://www.facebook.com/groups/591054007361950/posts/1234567892/",
        "https://www.facebook.com/groups/591054007361950/posts/1234567893/",
        "https://www.facebook.com/groups/591054007361950/posts/1234567894/"
    ]


@pytest.fixture
def mock_profile_config():
    """Mock browser profile configuration"""
    return {
        "profile_id": "test_profile_123",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "screen_resolution": "1920x1080",
        "timezone": "America/New_York",
        "proxy": {
            "type": "http",
            "host": "127.0.0.1",
            "port": 8080,
            "username": None,
            "password": None
        }
    }


@pytest.fixture
def mock_scraping_results():
    """Mock scraping results for testing"""
    return {
        "success": True,
        "results": {
            "uids_found": ["1111111111", "2222222222", "3333333333"],
            "total_uids_found": 3,
            "new_uids": ["1111111111", "2222222222", "3333333333"],
            "duplicate_uids": [],
            "scraping_time": 45.5,
            "comments_processed": 150,
            "scroll_iterations": 12,
            "post_url": "https://www.facebook.com/groups/123456789/posts/987654321/"
        },
        "metadata": {
            "profile_id": "test_profile_123",
            "max_scroll_time": 300,
            "extraction_method": "zendriver_advanced",
            "timestamp": "2024-01-15T10:30:00Z"
        }
    }


@pytest.fixture
def mock_performance_metrics():
    """Mock performance metrics for testing"""
    return {
        "timestamp": 1705312200.0,
        "system_metrics": {
            "cpu_percent": 45.2,
            "memory_percent": 62.8,
            "memory_used_mb": 1024.5,
            "disk_io_read_mb": 15.2,
            "disk_io_write_mb": 8.7,
            "network_sent_mb": 2.1,
            "network_recv_mb": 12.3,
            "active_threads": 8
        },
        "operation_summary": {
            "scrape_facebook_post_uids": {
                "total_executions": 25,
                "success_rate": 96.0,
                "error_rate": 4.0,
                "avg_duration_ms": 32500.0,
                "min_duration_ms": 15000.0,
                "max_duration_ms": 65000.0,
                "throughput_ops_per_sec": 0.083
            },
            "scrape_multiple_posts": {
                "total_executions": 8,
                "success_rate": 100.0,
                "error_rate": 0.0,
                "avg_duration_ms": 125000.0,
                "throughput_ops_per_sec": 0.028
            }
        },
        "metrics_summary": {
            "timer.scrape_facebook_post_uids": {
                "count": 25,
                "min": 15.0,
                "max": 65.0,
                "avg": 32.5,
                "median": 30.0,
                "std_dev": 12.3
            }
        },
        "performance_trends": {
            "system": {
                "cpu_trend": "stable",
                "memory_trend": "increasing",
                "sample_size": 120
            },
            "operations": {
                "scrape_facebook_post_uids": {
                    "response_time_trend": "stable",
                    "throughput": 3.0,
                    "sample_size": 25
                }
            }
        },
        "recent_anomalies": [],
        "baseline_established": True,
        "monitoring_active": True
    }


@pytest.fixture
def mock_error_scenarios():
    """Mock error scenarios for testing"""
    return {
        "network_timeout": Exception("Network timeout after 30 seconds"),
        "browser_crash": Exception("Browser process crashed unexpectedly"),
        "facebook_blocked": Exception("Facebook detected automation and blocked access"),
        "rate_limit": Exception("Rate limit exceeded, please try again later"),
        "parsing_error": Exception("Failed to parse comment structure"),
        "memory_error": MemoryError("Out of memory during processing"),
        "authentication_error": Exception("Facebook login session expired")
    }


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "unit: mark test as unit test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically"""
    for item in items:
        # Add integration marker to integration tests
        if "test_integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Add unit marker to unit tests
        elif "test_" in item.name and "integration" not in item.nodeid:
            item.add_marker(pytest.mark.unit)
        
        # Add slow marker to tests that might be slow
        if any(keyword in item.name.lower() for keyword in ["parallel", "memory", "performance"]):
            item.add_marker(pytest.mark.slow)


# Async test configuration
pytest_plugins = ('pytest_asyncio',)


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment"""
    # Set test environment variables
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "DEBUG"
    
    yield
    
    # Cleanup after tests
    if "TESTING" in os.environ:
        del os.environ["TESTING"]
    if "LOG_LEVEL" in os.environ:
        del os.environ["LOG_LEVEL"]


@pytest.fixture(autouse=True)
def reset_singletons():
    """Reset singleton instances between tests"""
    # Reset any singleton instances that might interfere with tests
    yield
    # Cleanup code here if needed


# Custom assertions for testing
def assert_valid_uid(uid):
    """Assert that a UID is valid"""
    assert isinstance(uid, str), f"UID must be string, got {type(uid)}"
    assert uid.isdigit(), f"UID must be numeric, got {uid}"
    assert len(uid) >= 8, f"UID must be at least 8 digits, got {len(uid)}"


def assert_valid_scraping_result(result):
    """Assert that a scraping result has valid structure"""
    assert isinstance(result, dict), "Result must be a dictionary"
    assert "success" in result, "Result must have 'success' field"
    
    if result["success"]:
        assert "results" in result, "Successful result must have 'results' field"
        results = result["results"]
        
        assert "uids_found" in results, "Results must have 'uids_found' field"
        assert "total_uids_found" in results, "Results must have 'total_uids_found' field"
        assert isinstance(results["uids_found"], list), "uids_found must be a list"
        assert isinstance(results["total_uids_found"], int), "total_uids_found must be an integer"
        
        # Validate each UID
        for uid in results["uids_found"]:
            assert_valid_uid(uid)
    else:
        assert "error" in result, "Failed result must have 'error' field"


def assert_valid_performance_report(report):
    """Assert that a performance report has valid structure"""
    assert isinstance(report, dict), "Report must be a dictionary"
    
    required_fields = [
        "timestamp", "system_metrics", "operation_summary", 
        "metrics_summary", "performance_trends", "recent_anomalies",
        "baseline_established", "monitoring_active"
    ]
    
    for field in required_fields:
        assert field in report, f"Report must have '{field}' field"
    
    # Validate system metrics
    system_metrics = report["system_metrics"]
    if system_metrics:
        assert "cpu_percent" in system_metrics
        assert "memory_percent" in system_metrics
        assert isinstance(system_metrics["cpu_percent"], (int, float))
        assert isinstance(system_metrics["memory_percent"], (int, float))


# Export custom assertions
__all__ = [
    "assert_valid_uid",
    "assert_valid_scraping_result", 
    "assert_valid_performance_report"
]
