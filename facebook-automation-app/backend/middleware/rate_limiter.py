"""
Rate limiting middleware for API protection
"""

import time
from typing import Dict, Callable
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from collections import defaultdict, deque
from loguru import logger

class RateLimiterMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware using sliding window algorithm"""
    
    def __init__(self, app, requests_per_minute: int = 60, requests_per_hour: int = 1000):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        
        # Store request timestamps for each client
        self.client_requests: Dict[str, deque] = defaultdict(lambda: deque())
        
        # Store blocked clients
        self.blocked_clients: Dict[str, float] = {}
        
        # Cleanup interval
        self.last_cleanup = time.time()
        self.cleanup_interval = 300  # 5 minutes
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Get client identifier
        client_ip = self._get_client_ip(request)
        
        # Skip rate limiting for health checks and internal endpoints
        if self._should_skip_rate_limiting(request):
            return await call_next(request)
        
        # Check if client is blocked
        if self._is_client_blocked(client_ip):
            logger.warning("Blocked client attempted request: {}", client_ip)
            raise HTTPException(
                status_code=429,
                detail="Too many requests. Client is temporarily blocked."
            )
        
        # Check rate limits
        if not self._check_rate_limits(client_ip):
            logger.warning(f"Rate limit exceeded for client: {client_ip}")
            
            # Block client if they exceed limits repeatedly
            self._block_client_if_needed(client_ip)
            
            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded. Please try again later.",
                headers={"Retry-After": "60"}
            )
        
        # Record the request
        self._record_request(client_ip)
        
        # Cleanup old data periodically
        self._cleanup_if_needed()
        
        # Process the request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining_requests = self._get_remaining_requests(client_ip)
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining_requests)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + 60)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def _should_skip_rate_limiting(self, request: Request) -> bool:
        """Check if rate limiting should be skipped for this request"""
        skip_paths = ["/health", "/", "/docs", "/redoc", "/openapi.json"]
        return request.url.path in skip_paths
    
    def _is_client_blocked(self, client_ip: str) -> bool:
        """Check if client is currently blocked"""
        if client_ip not in self.blocked_clients:
            return False
        
        block_time = self.blocked_clients[client_ip]
        current_time = time.time()
        
        # Unblock after 1 hour
        if current_time - block_time > 3600:
            del self.blocked_clients[client_ip]
            return False
        
        return True
    
    def _check_rate_limits(self, client_ip: str) -> bool:
        """Check if client is within rate limits"""
        current_time = time.time()
        client_requests = self.client_requests[client_ip]
        
        # Remove requests older than 1 hour
        while client_requests and current_time - client_requests[0] > 3600:
            client_requests.popleft()
        
        # Check hourly limit
        if len(client_requests) >= self.requests_per_hour:
            return False
        
        # Check minute limit
        minute_requests = sum(1 for req_time in client_requests 
                            if current_time - req_time <= 60)
        
        if minute_requests >= self.requests_per_minute:
            return False
        
        return True
    
    def _record_request(self, client_ip: str):
        """Record a request for the client"""
        current_time = time.time()
        self.client_requests[client_ip].append(current_time)
    
    def _get_remaining_requests(self, client_ip: str) -> int:
        """Get remaining requests for the client in current minute"""
        current_time = time.time()
        client_requests = self.client_requests[client_ip]
        
        minute_requests = sum(1 for req_time in client_requests 
                            if current_time - req_time <= 60)
        
        return max(0, self.requests_per_minute - minute_requests)
    
    def _block_client_if_needed(self, client_ip: str):
        """Block client if they exceed limits repeatedly"""
        current_time = time.time()
        client_requests = self.client_requests[client_ip]
        
        # Count requests in last 5 minutes
        recent_requests = sum(1 for req_time in client_requests 
                            if current_time - req_time <= 300)
        
        # Block if more than 5x the minute limit in 5 minutes
        if recent_requests > self.requests_per_minute * 5:
            self.blocked_clients[client_ip] = current_time
            logger.warning("Blocked client for excessive requests: {}", client_ip)
    
    def _cleanup_if_needed(self):
        """Cleanup old data periodically"""
        current_time = time.time()
        
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_data()
            self.last_cleanup = current_time
    
    def _cleanup_old_data(self):
        """Remove old request data to prevent memory leaks"""
        current_time = time.time()
        
        # Clean up client requests older than 1 hour
        for client_ip in list(self.client_requests.keys()):
            client_requests = self.client_requests[client_ip]
            
            # Remove old requests
            while client_requests and current_time - client_requests[0] > 3600:
                client_requests.popleft()
            
            # Remove empty deques
            if not client_requests:
                del self.client_requests[client_ip]
        
        # Clean up old blocked clients
        for client_ip in list(self.blocked_clients.keys()):
            block_time = self.blocked_clients[client_ip]
            if current_time - block_time > 3600:
                del self.blocked_clients[client_ip]
        
        logger.info(f"Cleaned up rate limiter data. Active clients: {len(self.client_requests)}")
    
    def get_rate_limit_stats(self) -> Dict[str, any]:
        """Get current rate limiting statistics"""
        current_time = time.time()
        
        stats = {
            "active_clients": len(self.client_requests),
            "blocked_clients": len(self.blocked_clients),
            "requests_per_minute_limit": self.requests_per_minute,
            "requests_per_hour_limit": self.requests_per_hour,
            "top_clients": []
        }
        
        # Get top clients by request count
        client_counts = []
        for client_ip, requests in self.client_requests.items():
            recent_count = sum(1 for req_time in requests 
                             if current_time - req_time <= 3600)
            if recent_count > 0:
                client_counts.append((client_ip, recent_count))
        
        # Sort by request count and take top 10
        client_counts.sort(key=lambda x: x[1], reverse=True)
        stats["top_clients"] = client_counts[:10]
        
        return stats

class APIKeyRateLimiter:
    """Rate limiter for API key based authentication"""
    
    def __init__(self):
        self.api_key_limits = {
            "free": {"requests_per_minute": 10, "requests_per_hour": 100},
            "premium": {"requests_per_minute": 100, "requests_per_hour": 5000},
            "enterprise": {"requests_per_minute": 1000, "requests_per_hour": 50000}
        }
        self.api_key_requests: Dict[str, deque] = defaultdict(lambda: deque())
    
    def check_api_key_limits(self, api_key: str, tier: str = "free") -> bool:
        """Check if API key is within limits"""
        if tier not in self.api_key_limits:
            return False
        
        limits = self.api_key_limits[tier]
        current_time = time.time()
        key_requests = self.api_key_requests[api_key]
        
        # Remove old requests
        while key_requests and current_time - key_requests[0] > 3600:
            key_requests.popleft()
        
        # Check limits
        hourly_count = len(key_requests)
        minute_count = sum(1 for req_time in key_requests 
                          if current_time - req_time <= 60)
        
        if (hourly_count >= limits["requests_per_hour"] or 
            minute_count >= limits["requests_per_minute"]):
            return False
        
        # Record request
        key_requests.append(current_time)
        return True
