#!/usr/bin/env python3
"""
Test runner for Facebook Scraper Service
Comprehensive test execution with reporting and coverage
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path
import time
from typing import List, Dict, Any


def setup_test_environment():
    """Setup test environment and dependencies"""
    print("🔧 Setting up test environment...")
    
    # Add backend directory to Python path
    backend_dir = Path(__file__).parent
    sys.path.insert(0, str(backend_dir))
    
    # Set environment variables
    os.environ["TESTING"] = "true"
    os.environ["PYTHONPATH"] = str(backend_dir)
    
    print("✅ Test environment setup complete")


def install_test_dependencies():
    """Install required test dependencies"""
    print("📦 Installing test dependencies...")
    
    dependencies = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-html>=3.1.0",
        "pytest-xdist>=3.0.0",  # For parallel test execution
        "coverage>=7.0.0"
    ]
    
    for dep in dependencies:
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], check=True, capture_output=True)
            print(f"  ✅ Installed {dep}")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Failed to install {dep}: {e}")
            return False
    
    print("✅ All test dependencies installed")
    return True


def run_unit_tests(verbose: bool = False, coverage: bool = True) -> Dict[str, Any]:
    """Run unit tests"""
    print("🧪 Running unit tests...")
    
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_facebook_scraper_service.py",
        "-m", "unit",
        "--tb=short"
    ]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend([
            "--cov=automation",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov/unit"
        ])
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    duration = time.time() - start_time
    
    return {
        "name": "Unit Tests",
        "success": result.returncode == 0,
        "duration": duration,
        "output": result.stdout,
        "errors": result.stderr
    }


def run_integration_tests(verbose: bool = False, coverage: bool = True) -> Dict[str, Any]:
    """Run integration tests"""
    print("🔗 Running integration tests...")
    
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_integration.py",
        "-m", "integration",
        "--tb=short"
    ]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend([
            "--cov=automation",
            "--cov=api",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov/integration"
        ])
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    duration = time.time() - start_time
    
    return {
        "name": "Integration Tests",
        "success": result.returncode == 0,
        "duration": duration,
        "output": result.stdout,
        "errors": result.stderr
    }


def run_performance_tests(verbose: bool = False) -> Dict[str, Any]:
    """Run performance tests"""
    print("⚡ Running performance tests...")
    
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-m", "performance",
        "--tb=short"
    ]
    
    if verbose:
        cmd.append("-v")
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    duration = time.time() - start_time
    
    return {
        "name": "Performance Tests",
        "success": result.returncode == 0,
        "duration": duration,
        "output": result.stdout,
        "errors": result.stderr
    }


def run_all_tests(verbose: bool = False, coverage: bool = True, parallel: bool = False) -> Dict[str, Any]:
    """Run all tests"""
    print("🚀 Running all tests...")
    
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "--tb=short",
        "--html=reports/test_report.html",
        "--self-contained-html"
    ]
    
    if verbose:
        cmd.append("-v")
    
    if parallel:
        cmd.extend(["-n", "auto"])  # Use all available CPUs
    
    if coverage:
        cmd.extend([
            "--cov=automation",
            "--cov=api",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov/all",
            "--cov-report=xml:coverage.xml"
        ])
    
    # Create reports directory
    os.makedirs("reports", exist_ok=True)
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    duration = time.time() - start_time
    
    return {
        "name": "All Tests",
        "success": result.returncode == 0,
        "duration": duration,
        "output": result.stdout,
        "errors": result.stderr
    }


def generate_coverage_report():
    """Generate comprehensive coverage report"""
    print("📊 Generating coverage report...")
    
    try:
        # Combine coverage data if multiple test runs
        subprocess.run([
            sys.executable, "-m", "coverage", "combine"
        ], check=False, capture_output=True)
        
        # Generate HTML report
        subprocess.run([
            sys.executable, "-m", "coverage", "html", "-d", "htmlcov/combined"
        ], check=True, capture_output=True)
        
        # Generate XML report for CI/CD
        subprocess.run([
            sys.executable, "-m", "coverage", "xml"
        ], check=True, capture_output=True)
        
        # Print coverage summary
        result = subprocess.run([
            sys.executable, "-m", "coverage", "report"
        ], capture_output=True, text=True)
        
        print("📈 Coverage Summary:")
        print(result.stdout)
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to generate coverage report: {e}")
        return False


def print_test_summary(results: List[Dict[str, Any]]):
    """Print test execution summary"""
    print("\n" + "="*60)
    print("📋 TEST EXECUTION SUMMARY")
    print("="*60)
    
    total_duration = 0
    passed_tests = 0
    failed_tests = 0
    
    for result in results:
        status = "✅ PASSED" if result["success"] else "❌ FAILED"
        duration = f"{result['duration']:.2f}s"
        
        print(f"{result['name']:<20} {status:<10} {duration}")
        
        total_duration += result["duration"]
        if result["success"]:
            passed_tests += 1
        else:
            failed_tests += 1
            print(f"  Error: {result['errors'][:200]}...")
    
    print("-" * 60)
    print(f"Total Tests: {len(results)}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Total Duration: {total_duration:.2f}s")
    print("="*60)
    
    if failed_tests > 0:
        print("\n❌ Some tests failed. Check the output above for details.")
        return False
    else:
        print("\n✅ All tests passed successfully!")
        return True


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="Facebook Scraper Service Test Runner")
    parser.add_argument("--type", choices=["unit", "integration", "performance", "all"], 
                       default="all", help="Type of tests to run")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--no-coverage", action="store_true", help="Disable coverage reporting")
    parser.add_argument("--parallel", "-p", action="store_true", help="Run tests in parallel")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    
    args = parser.parse_args()
    
    print("🧪 Facebook Scraper Service Test Runner")
    print("="*50)
    
    # Setup environment
    setup_test_environment()
    
    # Install dependencies if requested
    if args.install_deps:
        if not install_test_dependencies():
            sys.exit(1)
    
    # Run tests based on type
    results = []
    coverage = not args.no_coverage
    
    try:
        if args.type == "unit":
            results.append(run_unit_tests(args.verbose, coverage))
        elif args.type == "integration":
            results.append(run_integration_tests(args.verbose, coverage))
        elif args.type == "performance":
            results.append(run_performance_tests(args.verbose))
        elif args.type == "all":
            results.append(run_all_tests(args.verbose, coverage, args.parallel))
        
        # Generate coverage report if enabled
        if coverage and args.type in ["all", "unit", "integration"]:
            generate_coverage_report()
        
        # Print summary
        success = print_test_summary(results)
        
        if not success:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during test execution: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
