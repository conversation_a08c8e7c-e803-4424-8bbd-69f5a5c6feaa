#!/usr/bin/env python3
"""
Performance Testing Script for Facebook Scraper Service
Tests performance with real Facebook posts and provides optimization recommendations
"""

import asyncio
import time
import statistics
import json
import argparse
from typing import List, Dict, Any, Optional
from pathlib import Path
import sys
from datetime import datetime
import psutil
import gc

# Add backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from automation.facebook_scraper_service import FacebookScraperService


class PerformanceTester:
    """Performance testing suite for Facebook Scraper Service"""
    
    def __init__(self):
        self.scraper_service = None
        self.test_results = []
        self.system_stats = []
        
    async def setup(self):
        """Setup performance testing environment"""
        print("🔧 Setting up performance testing environment...")
        
        # Initialize scraper service
        self.scraper_service = FacebookScraperService()
        
        # Start performance monitoring
        await self.scraper_service.performance_monitor.start_monitoring()
        
        # Establish baseline
        print("📊 Establishing performance baseline...")
        await asyncio.sleep(5)  # Let monitoring collect some data
        self.scraper_service.performance_monitor.analyzer.establish_baseline()
        
        print("✅ Performance testing environment ready")
    
    async def cleanup(self):
        """Cleanup after performance testing"""
        if self.scraper_service:
            await self.scraper_service.cleanup()
    
    def collect_system_stats(self) -> Dict[str, Any]:
        """Collect current system statistics"""
        return {
            "timestamp": time.time(),
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "memory_used_mb": psutil.virtual_memory().used / 1024 / 1024,
            "available_memory_mb": psutil.virtual_memory().available / 1024 / 1024,
            "disk_usage_percent": psutil.disk_usage('/').percent,
            "active_processes": len(psutil.pids()),
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
        }
    
    async def test_single_post_performance(
        self, 
        profile_id: str, 
        post_url: str, 
        iterations: int = 5
    ) -> Dict[str, Any]:
        """Test single post scraping performance"""
        print(f"🧪 Testing single post performance ({iterations} iterations)...")
        
        results = []
        
        for i in range(iterations):
            print(f"  Iteration {i+1}/{iterations}")
            
            # Collect pre-test stats
            pre_stats = self.collect_system_stats()
            
            # Force garbage collection
            gc.collect()
            
            start_time = time.time()
            
            try:
                result = await self.scraper_service.scrape_facebook_post_uids(
                    profile_id=profile_id,
                    post_url=post_url,
                    max_scroll_time=60  # Shorter time for performance testing
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                # Collect post-test stats
                post_stats = self.collect_system_stats()
                
                results.append({
                    "iteration": i + 1,
                    "success": result.get("success", False),
                    "duration": duration,
                    "uids_found": len(result.get("results", {}).get("uids_found", [])),
                    "pre_stats": pre_stats,
                    "post_stats": post_stats,
                    "memory_delta": post_stats["memory_used_mb"] - pre_stats["memory_used_mb"],
                    "cpu_peak": max(pre_stats["cpu_percent"], post_stats["cpu_percent"])
                })
                
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                
                results.append({
                    "iteration": i + 1,
                    "success": False,
                    "duration": duration,
                    "error": str(e),
                    "pre_stats": pre_stats,
                    "post_stats": self.collect_system_stats()
                })
            
            # Wait between iterations
            await asyncio.sleep(2)
        
        # Calculate statistics
        successful_results = [r for r in results if r["success"]]
        
        if successful_results:
            durations = [r["duration"] for r in successful_results]
            memory_deltas = [r["memory_delta"] for r in successful_results]
            uids_counts = [r["uids_found"] for r in successful_results]
            
            stats = {
                "test_type": "single_post",
                "iterations": iterations,
                "successful_iterations": len(successful_results),
                "success_rate": len(successful_results) / iterations * 100,
                "duration_stats": {
                    "min": min(durations),
                    "max": max(durations),
                    "mean": statistics.mean(durations),
                    "median": statistics.median(durations),
                    "stdev": statistics.stdev(durations) if len(durations) > 1 else 0
                },
                "memory_stats": {
                    "min_delta": min(memory_deltas),
                    "max_delta": max(memory_deltas),
                    "mean_delta": statistics.mean(memory_deltas)
                },
                "uids_stats": {
                    "min_count": min(uids_counts),
                    "max_count": max(uids_counts),
                    "mean_count": statistics.mean(uids_counts)
                },
                "raw_results": results
            }
        else:
            stats = {
                "test_type": "single_post",
                "iterations": iterations,
                "successful_iterations": 0,
                "success_rate": 0,
                "error": "All iterations failed",
                "raw_results": results
            }
        
        self.test_results.append(stats)
        return stats
    
    async def test_parallel_performance(
        self, 
        profile_id: str, 
        post_urls: List[str], 
        max_concurrent_values: List[int] = [1, 2, 4, 8]
    ) -> Dict[str, Any]:
        """Test parallel processing performance with different concurrency levels"""
        print(f"🚀 Testing parallel performance with {len(post_urls)} posts...")
        
        results = {}
        
        for max_concurrent in max_concurrent_values:
            print(f"  Testing with max_concurrent={max_concurrent}")
            
            # Collect pre-test stats
            pre_stats = self.collect_system_stats()
            gc.collect()
            
            start_time = time.time()
            
            try:
                result = await self.scraper_service.scrape_multiple_posts_parallel(
                    profile_id=profile_id,
                    post_urls=post_urls,
                    max_concurrent=max_concurrent
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                # Collect post-test stats
                post_stats = self.collect_system_stats()
                
                results[max_concurrent] = {
                    "success": result.get("success", False),
                    "duration": duration,
                    "total_uids": len(result.get("results", {}).get("all_unique_uids", [])),
                    "posts_processed": result.get("results", {}).get("total_posts_processed", 0),
                    "throughput": len(post_urls) / duration if duration > 0 else 0,
                    "pre_stats": pre_stats,
                    "post_stats": post_stats,
                    "memory_delta": post_stats["memory_used_mb"] - pre_stats["memory_used_mb"],
                    "cpu_peak": max(pre_stats["cpu_percent"], post_stats["cpu_percent"])
                }
                
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                
                results[max_concurrent] = {
                    "success": False,
                    "duration": duration,
                    "error": str(e),
                    "pre_stats": pre_stats,
                    "post_stats": self.collect_system_stats()
                }
            
            # Wait between tests
            await asyncio.sleep(5)
        
        # Find optimal concurrency
        successful_results = {k: v for k, v in results.items() if v["success"]}
        
        if successful_results:
            # Find best throughput
            best_throughput = max(successful_results.items(), key=lambda x: x[1]["throughput"])
            
            # Find best efficiency (throughput / memory usage)
            best_efficiency = max(
                successful_results.items(), 
                key=lambda x: x[1]["throughput"] / max(x[1]["memory_delta"], 1)
            )
            
            stats = {
                "test_type": "parallel_performance",
                "post_count": len(post_urls),
                "concurrency_levels_tested": max_concurrent_values,
                "successful_tests": len(successful_results),
                "best_throughput": {
                    "max_concurrent": best_throughput[0],
                    "throughput": best_throughput[1]["throughput"],
                    "duration": best_throughput[1]["duration"]
                },
                "best_efficiency": {
                    "max_concurrent": best_efficiency[0],
                    "efficiency": best_efficiency[1]["throughput"] / max(best_efficiency[1]["memory_delta"], 1)
                },
                "detailed_results": results
            }
        else:
            stats = {
                "test_type": "parallel_performance",
                "post_count": len(post_urls),
                "successful_tests": 0,
                "error": "All parallel tests failed",
                "detailed_results": results
            }
        
        self.test_results.append(stats)
        return stats
    
    async def test_memory_optimization_performance(
        self, 
        profile_id: str, 
        post_urls: List[str]
    ) -> Dict[str, Any]:
        """Test memory optimization performance"""
        print(f"💾 Testing memory optimization with {len(post_urls)} posts...")
        
        # Test regular vs memory-optimized
        tests = [
            ("regular", self.scraper_service.scrape_multiple_posts),
            ("memory_optimized", self.scraper_service.scrape_with_memory_optimization)
        ]
        
        results = {}
        
        for test_name, test_method in tests:
            print(f"  Testing {test_name} mode...")
            
            # Collect pre-test stats
            pre_stats = self.collect_system_stats()
            gc.collect()
            
            start_time = time.time()
            
            try:
                result = await test_method(
                    profile_id=profile_id,
                    post_urls=post_urls
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                # Collect post-test stats
                post_stats = self.collect_system_stats()
                
                results[test_name] = {
                    "success": result.get("success", False),
                    "duration": duration,
                    "total_uids": len(result.get("results", {}).get("all_unique_uids", [])),
                    "posts_processed": result.get("results", {}).get("total_posts_processed", 0),
                    "pre_stats": pre_stats,
                    "post_stats": post_stats,
                    "memory_delta": post_stats["memory_used_mb"] - pre_stats["memory_used_mb"],
                    "peak_memory": max(pre_stats["memory_used_mb"], post_stats["memory_used_mb"]),
                    "memory_efficiency": len(result.get("results", {}).get("all_unique_uids", [])) / max(post_stats["memory_used_mb"] - pre_stats["memory_used_mb"], 1)
                }
                
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                
                results[test_name] = {
                    "success": False,
                    "duration": duration,
                    "error": str(e),
                    "pre_stats": pre_stats,
                    "post_stats": self.collect_system_stats()
                }
            
            # Wait between tests
            await asyncio.sleep(3)
        
        # Compare results
        if results["regular"]["success"] and results["memory_optimized"]["success"]:
            memory_improvement = (
                results["regular"]["memory_delta"] - results["memory_optimized"]["memory_delta"]
            ) / results["regular"]["memory_delta"] * 100
            
            duration_comparison = (
                results["memory_optimized"]["duration"] - results["regular"]["duration"]
            ) / results["regular"]["duration"] * 100
            
            stats = {
                "test_type": "memory_optimization",
                "post_count": len(post_urls),
                "memory_improvement_percent": memory_improvement,
                "duration_overhead_percent": duration_comparison,
                "regular_memory_delta": results["regular"]["memory_delta"],
                "optimized_memory_delta": results["memory_optimized"]["memory_delta"],
                "detailed_results": results
            }
        else:
            stats = {
                "test_type": "memory_optimization",
                "post_count": len(post_urls),
                "error": "One or both tests failed",
                "detailed_results": results
            }
        
        self.test_results.append(stats)
        return stats
    
    async def test_stress_performance(
        self, 
        profile_id: str, 
        post_urls: List[str], 
        duration_minutes: int = 10
    ) -> Dict[str, Any]:
        """Test system performance under sustained load"""
        print(f"🔥 Running stress test for {duration_minutes} minutes...")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        iteration = 0
        successful_operations = 0
        failed_operations = 0
        system_snapshots = []
        
        while time.time() < end_time:
            iteration += 1
            print(f"  Stress iteration {iteration}")
            
            # Collect system stats
            system_snapshots.append(self.collect_system_stats())
            
            try:
                # Randomly choose test type
                import random
                test_choice = random.choice([
                    "single_post",
                    "parallel_small",
                    "memory_optimized"
                ])
                
                if test_choice == "single_post":
                    result = await self.scraper_service.scrape_facebook_post_uids(
                        profile_id=profile_id,
                        post_url=random.choice(post_urls),
                        max_scroll_time=30
                    )
                elif test_choice == "parallel_small":
                    result = await self.scraper_service.scrape_multiple_posts_parallel(
                        profile_id=profile_id,
                        post_urls=random.sample(post_urls, min(3, len(post_urls))),
                        max_concurrent=2
                    )
                else:  # memory_optimized
                    result = await self.scraper_service.scrape_with_memory_optimization(
                        profile_id=profile_id,
                        post_urls=random.sample(post_urls, min(2, len(post_urls)))
                    )
                
                if result.get("success", False):
                    successful_operations += 1
                else:
                    failed_operations += 1
                    
            except Exception as e:
                failed_operations += 1
                print(f"    Error in iteration {iteration}: {e}")
            
            # Brief pause between operations
            await asyncio.sleep(1)
        
        # Analyze system performance over time
        if system_snapshots:
            cpu_values = [s["cpu_percent"] for s in system_snapshots]
            memory_values = [s["memory_percent"] for s in system_snapshots]
            
            stats = {
                "test_type": "stress_test",
                "duration_minutes": duration_minutes,
                "total_iterations": iteration,
                "successful_operations": successful_operations,
                "failed_operations": failed_operations,
                "success_rate": successful_operations / max(iteration, 1) * 100,
                "operations_per_minute": iteration / duration_minutes,
                "system_performance": {
                    "cpu_stats": {
                        "min": min(cpu_values),
                        "max": max(cpu_values),
                        "mean": statistics.mean(cpu_values),
                        "median": statistics.median(cpu_values)
                    },
                    "memory_stats": {
                        "min": min(memory_values),
                        "max": max(memory_values),
                        "mean": statistics.mean(memory_values),
                        "median": statistics.median(memory_values)
                    }
                },
                "system_snapshots": system_snapshots
            }
        else:
            stats = {
                "test_type": "stress_test",
                "duration_minutes": duration_minutes,
                "error": "No system snapshots collected"
            }
        
        self.test_results.append(stats)
        return stats
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        if not self.scraper_service:
            return {"error": "No scraper service available"}
        
        # Get performance monitor report
        monitor_report = self.scraper_service.get_performance_report()
        
        # Get scraping statistics
        scraping_stats = self.scraper_service.get_scraping_statistics()
        
        # Compile comprehensive report
        report = {
            "timestamp": datetime.now().isoformat(),
            "test_summary": {
                "total_tests": len(self.test_results),
                "test_types": list(set(r.get("test_type", "unknown") for r in self.test_results))
            },
            "test_results": self.test_results,
            "performance_monitor_report": monitor_report,
            "scraping_statistics": scraping_stats,
            "system_info": {
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": psutil.virtual_memory().total / 1024 / 1024 / 1024,
                "python_version": sys.version
            }
        }
        
        return report
    
    def save_report(self, report: Dict[str, Any], filename: str = None):
        """Save performance report to file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Performance report saved to {filename}")
    
    def print_summary(self, report: Dict[str, Any]):
        """Print performance test summary"""
        print("\n" + "="*60)
        print("📊 PERFORMANCE TEST SUMMARY")
        print("="*60)
        
        print(f"Total Tests: {report['test_summary']['total_tests']}")
        print(f"Test Types: {', '.join(report['test_summary']['test_types'])}")
        
        for result in report["test_results"]:
            test_type = result.get("test_type", "unknown")
            print(f"\n{test_type.upper()}:")
            
            if "success_rate" in result:
                print(f"  Success Rate: {result['success_rate']:.1f}%")
            
            if "duration_stats" in result:
                duration = result["duration_stats"]
                print(f"  Duration: {duration['mean']:.2f}s (±{duration['stdev']:.2f}s)")
            
            if "best_throughput" in result:
                throughput = result["best_throughput"]
                print(f"  Best Throughput: {throughput['throughput']:.2f} posts/sec @ {throughput['max_concurrent']} concurrent")
            
            if "memory_improvement_percent" in result:
                print(f"  Memory Improvement: {result['memory_improvement_percent']:.1f}%")
        
        print("="*60)


async def run_mock_performance_test():
    """Run performance test with mock data for testing purposes"""
    print("🧪 Running mock performance test...")

    # Mock data
    profile_id = "test_profile_performance"
    post_urls = [
        "https://www.facebook.com/groups/123456789/posts/mock_post_1/",
        "https://www.facebook.com/groups/123456789/posts/mock_post_2/",
        "https://www.facebook.com/groups/123456789/posts/mock_post_3/",
        "https://www.facebook.com/groups/123456789/posts/mock_post_4/",
        "https://www.facebook.com/groups/123456789/posts/mock_post_5/"
    ]

    tester = PerformanceTester()

    try:
        await tester.setup()

        # Mock the scraper service methods for testing
        from unittest.mock import AsyncMock, patch

        # Mock single post scraping
        async def mock_single_scrape(*args, **kwargs):
            await asyncio.sleep(0.5)  # Simulate processing time
            return {
                "success": True,
                "results": {
                    "uids_found": [f"mock_uid_{i}" for i in range(10)],
                    "total_uids_found": 10,
                    "new_uids": [f"mock_uid_{i}" for i in range(10)],
                    "duplicate_uids": [],
                    "scraping_time": 0.5
                }
            }

        # Mock parallel scraping
        async def mock_parallel_scrape(*args, **kwargs):
            max_concurrent = kwargs.get("max_concurrent", 2)
            post_count = len(kwargs.get("post_urls", []))
            await asyncio.sleep(max(0.5, post_count / max_concurrent))  # Simulate parallel processing
            return {
                "success": True,
                "results": {
                    "all_unique_uids": [f"parallel_uid_{i}" for i in range(post_count * 8)],
                    "total_posts_processed": post_count,
                    "performance_metrics": {
                        "max_concurrent_workers": max_concurrent,
                        "avg_processing_time": 0.5
                    }
                }
            }

        # Mock memory optimized scraping
        async def mock_memory_scrape(*args, **kwargs):
            post_count = len(kwargs.get("post_urls", []))
            await asyncio.sleep(post_count * 0.3)  # Simulate memory-optimized processing
            return {
                "success": True,
                "results": {
                    "all_unique_uids": [f"memory_uid_{i}" for i in range(post_count * 6)],
                    "total_posts_processed": post_count,
                    "memory_stats": {
                        "peak_memory_mb": 150.0,
                        "memory_optimized": True,
                        "streaming_enabled": True
                    }
                }
            }

        # Apply mocks
        with patch.object(tester.scraper_service, 'scrape_facebook_post_uids', mock_single_scrape):
            with patch.object(tester.scraper_service, 'scrape_multiple_posts_parallel', mock_parallel_scrape):
                with patch.object(tester.scraper_service, 'scrape_multiple_posts', mock_parallel_scrape):
                    with patch.object(tester.scraper_service, 'scrape_with_memory_optimization', mock_memory_scrape):

                        # Run performance tests
                        print("🧪 Running single post performance test...")
                        await tester.test_single_post_performance(profile_id, post_urls[0], 3)

                        print("🚀 Running parallel performance test...")
                        await tester.test_parallel_performance(profile_id, post_urls[:3], [1, 2, 4])

                        print("💾 Running memory optimization test...")
                        await tester.test_memory_optimization_performance(profile_id, post_urls[:2])

                        print("🔥 Running mini stress test...")
                        await tester.test_stress_performance(profile_id, post_urls, 1)  # 1 minute stress test

        # Generate and save report
        report = tester.generate_performance_report()
        tester.save_report(report, "mock_performance_report.json")
        tester.print_summary(report)

        print("\n✅ Mock performance test completed successfully!")

    except Exception as e:
        print(f"\n❌ Error during mock performance testing: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await tester.cleanup()


async def main():
    """Main performance testing function"""
    parser = argparse.ArgumentParser(description="Facebook Scraper Performance Tester")
    parser.add_argument("--profile-id", help="Browser profile ID to use")
    parser.add_argument("--post-urls", nargs="+", help="Facebook post URLs to test")
    parser.add_argument("--single-iterations", type=int, default=5, help="Iterations for single post test")
    parser.add_argument("--parallel-concurrency", nargs="+", type=int, default=[1, 2, 4], help="Concurrency levels to test")
    parser.add_argument("--stress-minutes", type=int, default=5, help="Stress test duration in minutes")
    parser.add_argument("--output", help="Output file for performance report")
    parser.add_argument("--skip-single", action="store_true", help="Skip single post performance test")
    parser.add_argument("--skip-parallel", action="store_true", help="Skip parallel performance test")
    parser.add_argument("--skip-memory", action="store_true", help="Skip memory optimization test")
    parser.add_argument("--skip-stress", action="store_true", help="Skip stress test")
    parser.add_argument("--mock", action="store_true", help="Run with mock data for testing")

    args = parser.parse_args()

    # Run mock test if requested
    if args.mock:
        await run_mock_performance_test()
        return

    # Validate required arguments for real testing
    if not args.profile_id or not args.post_urls:
        print("❌ --profile-id and --post-urls are required for real performance testing")
        print("💡 Use --mock flag to run with mock data for testing")
        return

    tester = PerformanceTester()

    try:
        await tester.setup()

        # Run performance tests
        if not args.skip_single:
            await tester.test_single_post_performance(
                args.profile_id,
                args.post_urls[0],
                args.single_iterations
            )

        if not args.skip_parallel and len(args.post_urls) > 1:
            await tester.test_parallel_performance(
                args.profile_id,
                args.post_urls,
                args.parallel_concurrency
            )

        if not args.skip_memory and len(args.post_urls) > 1:
            await tester.test_memory_optimization_performance(
                args.profile_id,
                args.post_urls
            )

        if not args.skip_stress:
            await tester.test_stress_performance(
                args.profile_id,
                args.post_urls,
                args.stress_minutes
            )

        # Generate and save report
        report = tester.generate_performance_report()
        tester.save_report(report, args.output)
        tester.print_summary(report)

    except KeyboardInterrupt:
        print("\n⚠️  Performance testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during performance testing: {e}")
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
